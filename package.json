{"name": "tripppy", "version": "0.1.0", "bin": {"tripppy": "bin/tripppy.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy:init": "cdk deploy -c init", "deploy:frontend": "cd web && npm run build && cd .. && JSII_SILENCE_WARNING_UNTESTED_NODE_VERSION=true cdk deploy -c stack=frontend", "cleanup": "npx ts-node test/cleanup.ts", "tail": "test/scripts/tail.sh", "email": "aws ses send-email --from zlatko.su<PERSON><EMAIL> --to <EMAIL> --subject \"Test Email\" --text \"This is a test email\""}, "devDependencies": {"@types/aws-sdk": "^0.0.42", "@types/html-to-text": "^9.0.4", "@types/jest": "^29.5.14", "@types/mailparser": "^3.4.5", "@types/node": "22.7.9", "@types/striptags": "^0.0.5", "aws-cdk": "2.1000.2", "esbuild": "^0.25.0", "jest": "^29.7.0", "prettier": "^3.6.2", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"@ai-sdk/perplexity": "^1.1.9", "@aws-lambda-powertools/commons": "^2.17.0", "@aws-sdk/client-cognito-identity-provider": "^3.750.0", "@aws-sdk/client-dynamodb": "^3.751.0", "@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/client-ses": "^3.808.0", "@aws-sdk/client-ssm": "^3.806.0", "@aws-sdk/lib-dynamodb": "^3.751.0", "@aws-sdk/s3-request-presigner": "^3.750.0", "@langchain/community": "^0.3.37", "@langchain/core": "^0.3.43", "@langchain/textsplitters": "^0.1.0", "@pinecone-database/pinecone": "^5.1.0", "@types/aws-lambda": "^8.10.148", "ai": "^4.3.16", "aws-cdk-lib": "^2.180.0", "change-case": "^5.4.4", "constructs": "^10.4.2", "dedent": "^1.6.0", "html-to-text": "^9.0.5", "mailgun.js": "^12.0.1", "mailparser": "^3.7.2", "openai": "^4.92.0", "pdf-parse": "^1.1.1", "striptags": "^3.2.0", "telegramify-markdown": "^1.3.0"}}