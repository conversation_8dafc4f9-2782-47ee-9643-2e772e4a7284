import { WebPDFLoader } from '@langchain/community/document_loaders/web/pdf';
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';
import { htmlToText } from 'html-to-text';

const CHUNK_SIZE = 400;
const CHUNK_OVERLAP = 100;

const extractText = async (content: Buffer, contentType?: string): Promise<string> => {
  switch (contentType) {
    case 'application/pdf':
      const loader = new WebPDFLoader(new Blob([content], { type: contentType }));
      const pages = await loader.load();
      return pages.map((page) => page.pageContent).join('\n\n');
    case 'text/plain':
      return content.toString('utf-8');
    case 'message/rfc822':
      return htmlToText(content.toString('utf-8'), { preserveNewlines: true });
    default:
      throw new Error('Unsupported document type');
  }
};

export const getChunks = async (content: Buffer, contentType?: string): Promise<string[]> => {
  const text = await extractText(content, contentType);
  // .replace(/\\n/g, '\n')
  // .replace(/\*([^*]+)\*/g, '$1') // Remove asterisks used for formatting
  // .replace(/\[image:[^\]]+\]/g, '') // Remove image placeholders
  // .replace(/\n{3,}/g, '\n\n') // Normalize excessive newlines
  // .replace(/\*([^*]+)\*/g, '$1') // Remove asterisks
  // .replace(/\[image:[^\]]+\]/g, '') // Remove image markers
  // .trim();
  // const text = extractedText
  //   .replace(/\\n/g, '\n') // Replace \n with actual newline
  //   .replace(/\\\\n/g, '\n') // Handle double-escaped newlines
  //   .replace(/\\r\\n/g, '\n') // Handle escaped Windows newlines
  //   .replace(/\\r/g, '\n') // Handle escaped carriage returns
  //   .replace(/\r\n/g, '\n') // Normalize Windows newlines
  //   .replace(/\r/g, '\n') // Handle single carriage returns
  //   .replace(/\n{3,}/g, '\n\n'); // Limit consecutive newlines

  console.log('Extracted text:', text);

  const splitter = new RecursiveCharacterTextSplitter({
    chunkSize: CHUNK_SIZE,
    chunkOverlap: CHUNK_OVERLAP,
    separators: ['\n', ' ', '.'],
  });

  const chunks = await splitter.splitText(text);

  return chunks.map((chunk) =>
    chunk
      .replace(/\\n/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .trim()
  );
};
