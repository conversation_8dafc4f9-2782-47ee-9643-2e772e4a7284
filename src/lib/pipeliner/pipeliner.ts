export interface CommandOptions<Context = undefined, PipelineInput = undefined> {
  context?: Context;
  pipelineInput?: PipelineInput;
  // ...other options
}

export interface Command<I, O, Context = undefined, PipelineInput = undefined> {
  execute(input: I, options?: CommandOptions<Context, PipelineInput>): Promise<O>;
}

export class PassThrough<T> implements Command<T, T> {
  async execute(input: T): Promise<T> {
    return input;
  }
}

// Pipeline builder that automatically infers output type
export class PipelineBuilder<I, Context = undefined> {
  private readonly steps: Command<any, any, Context, I>[];

  constructor(steps: Command<any, any, Context, I>[] = []) {
    this.steps = steps;
  }

  pipe<O>(command: Command<I, O, Context, I>): PipelineBuilderWithOutput<I, O, Context> {
    // Do not carry over error handler
    return new PipelineBuilderWithOutput<I, O, Context>([...this.steps, command]);
  }

  parallel<T extends readonly [Command<I, any, Context, I>, ...Command<I, any, Context, I>[]]>(
    commands: T,
  ): PipelineBuilderWithOutput<
    I,
    { [K in keyof T]: T[K] extends Command<I, infer Out, Context, I> ? Out : never },
    Context
  > {
    const parallelCommand: Command<
      I,
      { [K in keyof T]: T[K] extends Command<I, infer Out, Context, I> ? Out : never },
      Context,
      I
    > = {
      async execute(input: I, options?: CommandOptions<Context, I>) {
        const results = await Promise.all(commands.map((c) => c.execute(input, options)));
        return results as {
          [K in keyof T]: T[K] extends Command<I, infer Out, Context, I> ? Out : never;
        };
      },
    };
    // Do not carry over error handler
    return new PipelineBuilderWithOutput([...this.steps, parallelCommand]);
  }
}

class PipelineBuilderWithOutput<I, O, Context = undefined> {
  private readonly steps: Command<any, any, Context, I>[];
  private readonly errorHandler?: (error: Error, stepIndex: number) => Promise<O> | O;

  constructor(
    steps: Command<any, any, Context, I>[],
    errorHandler?: (error: Error, stepIndex: number) => Promise<O> | O,
  ) {
    this.steps = steps;
    this.errorHandler = errorHandler;
  }

  pipe<N>(command: Command<O, N, Context, I>): PipelineBuilderWithOutput<I, N, Context> {
    // Do not carry over error handler
    return new PipelineBuilderWithOutput<I, N, Context>([...this.steps, command]);
  }

  parallel<T extends readonly [Command<O, any, Context, I>, ...Command<O, any, Context, I>[]]>(
    commands: T,
  ): PipelineBuilderWithOutput<
    I,
    { [K in keyof T]: T[K] extends Command<O, infer Out, Context, I> ? Out : never },
    Context
  > {
    const parallelCommand: Command<
      O,
      { [K in keyof T]: T[K] extends Command<O, infer Out, Context, I> ? Out : never },
      Context,
      I
    > = {
      async execute(input: O, options?: CommandOptions<Context, I>) {
        const results = await Promise.all(commands.map((c) => c.execute(input, options)));
        return results as {
          [K in keyof T]: T[K] extends Command<O, infer Out, Context, I> ? Out : never;
        };
      },
    };
    // Do not carry over error handler
    return new PipelineBuilderWithOutput([...this.steps, parallelCommand]);
  }

  branch<A, B>(
    condition: (input: O, options?: CommandOptions<Context, I>) => boolean | Promise<boolean>,
    thenBuilder: (p: PipelineBuilder<O, Context>) => PipelineBuilderWithOutput<O, A, Context>,
    elseBuilder: (p: PipelineBuilder<O, Context>) => PipelineBuilderWithOutput<O, B, Context>,
  ): PipelineBuilderWithOutput<I, A | B, Context> {
    const branchCommand: Command<O, A | B, Context, I> = {
      async execute(input: O, options?: CommandOptions<Context, I>) {
        const conditionResult = await condition(input, options);
        const builder = conditionResult
          ? thenBuilder(new PipelineBuilder<O, Context>())
          : elseBuilder(new PipelineBuilder<O, Context>());
        return builder.run(input, options?.context);
      },
    };
    // Do not carry over error handler
    return new PipelineBuilderWithOutput<I, A | B, Context>([...this.steps, branchCommand]);
  }

  catch(handler: (error: Error, stepIndex: number) => Promise<O> | O): PipelineBuilderWithOutput<I, O, Context> {
    return new PipelineBuilderWithOutput(this.steps, handler);
  }

  switch<
    Cases extends Array<{
      when: (input: O, options?: CommandOptions<Context, I>) => boolean | Promise<boolean>;
      build: (builder: PipelineBuilder<O, Context>) => PipelineBuilderWithOutput<O, any, Context>;
    }>,
  >(
    cases: Cases,
  ): PipelineBuilderWithOutput<
    I,
    ReturnType<Cases[number]['build']>['run'] extends (input: any, context?: any) => Promise<infer R> ? R : never,
    Context
  > {
    const switchCommand: Command<O, any, Context, I> = {
      async execute(input: O, options?: CommandOptions<Context, I>) {
        for (const { when, build } of cases) {
          if (await when(input, options)) {
            const branch = build(new PipelineBuilder<O, Context>());
            return branch.run(input, options?.context);
          }
        }
        throw new Error('No switch case matched');
      },
    };
    // Do not carry over error handler
    return new PipelineBuilderWithOutput([...this.steps, switchCommand]);
  }

  async run(input: I, context?: Context): Promise<O> {
    let current: any = input;
    const options: CommandOptions<Context, I> = { context, pipelineInput: input };
    for (let i = 0; i < this.steps.length; i++) {
      try {
        current = await this.steps[i].execute(current, options);
      } catch (error) {
        if (this.errorHandler) {
          return await this.errorHandler(error as Error, i);
        }
        throw error;
      }
    }
    return current;
  }
}
