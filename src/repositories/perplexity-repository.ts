import { createPerplexity, PerplexityProvider } from '@ai-sdk/perplexity';
import { LanguageModelV1Source } from '@ai-sdk/provider';
import { generateText } from 'ai';

export class PerplexityRepository {
  private readonly perplexity: PerplexityProvider;

  constructor(apiKey: string) {
    this.perplexity = createPerplexity({
      apiKey,
    });
  }

  async ask(prompt: string): Promise<{ text: string; sources: LanguageModelV1Source[] }> {
    const { text, sources } = await generateText({
      model: this.perplexity('sonar'),
      prompt,
    });

    return {
      text,
      sources,
    };
  }
}
