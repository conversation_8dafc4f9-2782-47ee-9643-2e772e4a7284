import { OpenAI } from 'openai';
import { Index, Pinecone, PineconeRecord, RecordMetadata, ScoredPineconeRecord } from '@pinecone-database/pinecone';
import { PINECONE_INDEX_NAME } from './constants';
import { TripDocumentMetadata } from '@shared/types/trip';
import { getChunks } from '../utils/text-splitter';

export interface PineconeMetadata extends RecordMetadata {
  userId: string;
  tripId: string;
  documentId: string;
  documentName: string;
  text: string;
}

export interface PineconeOptions {
  topK: number;
}

export class PineconeRepository {
  private readonly pinecone: Pinecone;
  private readonly openai: OpenAI;
  private readonly index: Index<PineconeMetadata>;

  constructor(pineconeApiKey: string, openaiApiKey: string, private options: PineconeOptions) {
    this.pinecone = new Pinecone({
      apiKey: pineconeApiKey,
      maxRetries: 5,
    });

    this.openai = new OpenAI({
      apiKey: openaiApiKey,
    });

    this.index = this.pinecone.Index(PINECONE_INDEX_NAME);
  }

  async upsertEmbeddings(userId: string, tripId: string, document: TripDocumentMetadata, chunks: string[]) {
    // const chunks = await getChunks(content, document.contentType);

    const vectors: Array<PineconeRecord<PineconeMetadata>> = await Promise.all(
      chunks.map(async (chunk) => ({
        id: crypto.randomUUID(),
        values: await this.generateEmbedding(chunk),
        metadata: {
          userId,
          tripId,
          documentId: document.id,
          documentName: document.name || document.id,
          text: chunk,
        },
      }))
    );

    return this.index.upsert(vectors);
  }

  async queryEmbedding(
    text: string,
    userId: string,
    tripId: string | null
  ): Promise<ScoredPineconeRecord<PineconeMetadata>[]> {
    const vector = await this.generateEmbedding(text);

    const result = await this.index.query({
      vector,
      topK: this.options.topK,
      includeValues: false,
      includeMetadata: true,
      filter: {
        userId: { $eq: userId },
        ...(tripId ? { tripId: { $eq: tripId } } : {}),
      },
    });

    return result.matches;
  }

  async deleteEmbeddings(userId: string, tripId: string, documentIds?: string[]): Promise<void> {
    return this.index.deleteMany({
      filter: {
        userId: { $eq: userId },
        tripId: { $eq: tripId },
        ...(documentIds?.length ? { documentId: { $in: documentIds } } : {}),
      },
    });
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    const response = await this.openai.embeddings.create({
      model: 'text-embedding-ada-002',
      input: text,
    });

    return response.data[0].embedding;
  }
}
