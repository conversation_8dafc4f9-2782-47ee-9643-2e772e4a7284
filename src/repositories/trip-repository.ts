import {
  DynamoDBDocumentClient,
  NativeAttributeValue,
  PutCommand,
  PutCommandOutput,
  UpdateCommand,
  GetCommand,
  QueryCommand,
  DeleteCommand,
  DeleteCommandOutput,
} from '@aws-sdk/lib-dynamodb';
import { TABLE_NAME } from './constants';
import { Trip } from '@shared/types/trip';
import { getISODate } from '@shared/utils';

export enum TripPeriod {
  UPCOMING = 'upcoming',
  PAST = 'past',
  DEFAULT = 'default',
}

export class TripRepository {
  constructor(private readonly client: DynamoDBDocumentClient) {}

  async createTrip(userId: string, trip: Trip): Promise<PutCommandOutput> {
    

    const now = new Date().toISOString();

    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: {
        PK: `USER#${userId}`,
        SK: `TRIP#${trip.id}`,
        name: trip.name,
        dateFrom: trip.dateFrom,
        dateTo: trip.dateTo,
        companions: trip.companions,
        description: trip.description,
        documents: trip.documents,
        createdAt: now,
        updatedAt: undefined,
      },
      ConditionExpression: 'attribute_not_exists(SK)',
    });

    return this.client.send(command);
  }

  async updateTrip(userId: string, trip: Trip): Promise<Record<string, NativeAttributeValue> | undefined> {
    const now = new Date().toISOString();

    type UpdateableTrip = Omit<Trip, 'id' | 'createdAt'>;

    const fields = Object.keys(trip).filter((key): key is keyof UpdateableTrip => key in trip);

    const updateExpression =
      'SET ' + [...fields.map((field) => `#${field} = :${field}`), '#updatedAt = :updatedAt'].join(', ');

    const expressionAttributeNames = Object.fromEntries([
      ...fields.map((field) => [`#${field}`, field]),
      ['#updatedAt', 'updatedAt'],
    ]);

    const expressionAttributeValues = Object.fromEntries([
      ...fields.map((field) => [`:${field}`, trip[field]]),
      [':updatedAt', now],
    ]);

    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: {
        PK: `USER#${userId}`,
        SK: `TRIP#${trip.id}`,
      },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: 'ALL_OLD',
    });

    const response = await this.client.send(command);
    return response.Attributes;
  }

  async getTrip(userId: string, tripId: string): Promise<Trip | null> {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: {
        PK: `USER#${userId}`,
        SK: `TRIP#${tripId}`,
      },
    });

    const response = await this.client.send(command);
    if (!response.Item) return null;

    return {
      id: tripId,
      name: response.Item.name,
      dateFrom: response.Item.dateFrom,
      dateTo: response.Item.dateTo,
      companions: response.Item.companions,
      description: response.Item.description,
      documents: response.Item.documents,
      createdAt: response.Item.createdAt,
      updatedAt: response.Item.updatedAt,
    };
  }

  async getTrips(userId: string, period?: TripPeriod): Promise<Trip[]> {
    const command = new QueryCommand({
      TableName: TABLE_NAME,
      KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
      ExpressionAttributeValues: {
        ':pk': `USER#${userId}`,
        ':sk': 'TRIP#',
      },
    });

    const response = await this.client.send(command);
    const now = getISODate();

    const filterTrips = (trip: Trip) => {
      switch (period) {
        case TripPeriod.DEFAULT:
          return !trip.dateFrom && !trip.dateTo;
        case TripPeriod.UPCOMING:
          return trip.dateFrom ? trip.dateFrom >= now : false;
        case TripPeriod.PAST:
          return trip.dateFrom ? trip.dateFrom < now : false;
        default:
          return true;
      }
    };

    return (response.Items || [])
      .map((item) => ({
        id: item.SK.replace('TRIP#', ''),
        name: item.name,
        dateFrom: item.dateFrom,
        dateTo: item.dateTo,
        companions: item.companions,
        description: item.description,
        documents: item.documents,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      }))
      .filter(filterTrips)
      .sort((a, b) => {
        const dateA = new Date(a.dateFrom).getTime();
        const dateB = new Date(b.dateFrom).getTime();
        return period === undefined
          ? dateA - dateB // Default: chronological
          : TripPeriod.UPCOMING
          ? dateA - dateB // Upcoming: nearest first
          : dateB - dateA; // Past: most recent first
      });
  }

  async appendToArrayAttribute(userId: string, tripId: string, attributeName: string, itemsToAppend: unknown[]) {
    const updateParams = {
      TableName: TABLE_NAME,
      Key: {
        PK: `USER#${userId}`,
        SK: `TRIP#${tripId}`,
      },
      UpdateExpression: `
      SET ${attributeName} = list_append(
        if_not_exists(${attributeName}, :empty_list),
        :new_items
      )
    `,
      ExpressionAttributeValues: {
        ':new_items': itemsToAppend,
        ':empty_list': [],
      },
      ReturnValues: 'UPDATED_NEW' as const,
    };

    return this.client.send(new UpdateCommand(updateParams));
  }

  async deleteTrip(userId: string, tripId: string): Promise<DeleteCommandOutput> {
    const command = new DeleteCommand({
      TableName: TABLE_NAME,
      Key: {
        PK: `USER#${userId}`,
        SK: `TRIP#${tripId}`,
      },
      ReturnValues: 'ALL_OLD',
    });

    return this.client.send(command);
  }
}
