import {
  CopyObjectCommand,
  DeleteO<PERSON><PERSON><PERSON>ommand,
  DeleteO<PERSON><PERSON>ommand,
  Get<PERSON><PERSON><PERSON>ommand,
  PutO<PERSON>Command,
  S3Client,
  DeleteObjectTaggingCommand,
} from '@aws-sdk/client-s3';
import { BUCKET_NAME } from './constants';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

export class S3Repository {
  constructor(private readonly client: S3Client) {}

  async saveFile(filename: string, data: Buffer, contentType: string) {
    return this.client.send(
      new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: filename,
        Body: data,
        ContentType: contentType,
      })
    );
  }

  async confirmFile(filename: string) {
    // Remove the 'temporary=true' tag from the object
    return this.client.send(
      new DeleteObjectTaggingCommand({
        Bucket: BUCKET_NAME,
        Key: filename,
      })
    );
  }

  async getFile(filename: string): Promise<Buffer> {
    const file = await this.client.send(
      new GetObjectCommand({
        Bucket: BUCKET_NAME,
        Key: filename,
      })
    );

    const content = await file.Body!.transformToByteArray();
    return Buffer.from(content);
  }

  async deleteFiles(filenames: string[]) {
    if (!filenames.length) return;

    return this.client.send(
      new DeleteObjectsCommand({
        Bucket: BUCKET_NAME,
        Delete: {
          Objects: filenames.map((filename) => ({ Key: filename })),
          Quiet: true, // Set to false if you want to get the list of deleted objects
        },
      })
    );
  }

  async getDownloadUrl(filename: string, expiresIn: number = 3600): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: filename,
    });

    return getSignedUrl(this.client, command, { expiresIn });
  }

  async getUploadUrl(filename: string, contentType: string, expiresIn: number = 3600): Promise<string> {
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: filename,
      ContentType: contentType,
      Tagging: 'temporary=true',
    });

    return getSignedUrl(this.client, command, {
      expiresIn,
    });
  }

  async moveFile(sourceFilename: string, destinationFilename: string, contentType?: string): Promise<void> {
    // First copy the file to the new location
    await this.client.send(
      new CopyObjectCommand({
        Bucket: BUCKET_NAME,
        CopySource: `${BUCKET_NAME}/${sourceFilename}`,
        Key: destinationFilename,
        ContentType: contentType || 'application/octet-stream', // Set new content type
      })
    );

    // Then delete the original file
    await this.client.send(
      new DeleteObjectCommand({
        Bucket: BUCKET_NAME,
        Key: sourceFilename,
      })
    );
  }
}
