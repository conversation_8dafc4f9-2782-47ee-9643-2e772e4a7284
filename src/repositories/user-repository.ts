import {
  DynamoDBDocumentClient,
  Get<PERSON>ommand,
  <PERSON><PERSON><PERSON>mand,
  Up<PERSON><PERSON>ommand,
  Delete<PERSON>ommand,
  QueryCommand,
} from '@aws-sdk/lib-dynamodb';
import { TABLE_NAME } from './constants';

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  createdAt?: string;
  updatedAt?: string;
}

export type GuestProfile = Pick<UserProfile, 'email' | 'createdAt' | 'updatedAt'>;

export class UserRepository {
  constructor(private readonly client: DynamoDBDocumentClient) {}

  async getUser(id: string): Promise<UserProfile | null> {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: {
        PK: `USER#${id}`,
        SK: 'PROFILE',
      },
    });

    const response = await this.client.send(command);
    return (response.Item as UserProfile) ?? null;
  }

  async getUserByEmail(email: string): Promise<UserProfile | null> {
    const command = new QueryCommand({
      TableName: TABLE_NAME,
      IndexName: 'EmailIndex', // Assuming you have a GSI on email
      KeyConditionExpression: 'email = :email',
      ExpressionAttributeValues: {
        ':email': email,
      },
    });

    const response = await this.client.send(command);
    return (response.Items && (response.Items[0] as UserProfile)) ?? null;
  }

  async createUser(user: UserProfile): Promise<void> {
    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: {
        PK: `USER#${user.id}`,
        SK: 'PROFILE',
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      ConditionExpression: 'attribute_not_exists(PK)',
    });

    await this.client.send(command);
  }

  async updateUser(id: string, updates: Partial<Omit<UserProfile, 'userId'>>): Promise<void> {
    const updateExpressions = Object.keys(updates).map((key, index) => `#${key} = :val${index}`);
    const expressionAttributeNames = Object.fromEntries(Object.keys(updates).map((key) => [`#${key}`, key]));
    const expressionAttributeValues = Object.fromEntries(
      Object.entries(updates).map(([key, value], index) => [`:val${index}`, value])
    );

    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: {
        PK: `USER#${id}`,
        SK: 'PROFILE',
      },
      UpdateExpression: `SET ${updateExpressions.join(', ')}, updatedAt = :updatedAt`,
      ExpressionAttributeNames: {
        ...expressionAttributeNames,
        '#updatedAt': 'updatedAt',
      },
      ExpressionAttributeValues: {
        ...expressionAttributeValues,
        ':updatedAt': new Date().toISOString(),
      },
    });

    await this.client.send(command);
  }

  async deleteUser(id: string): Promise<void> {
    const command = new DeleteCommand({
      TableName: TABLE_NAME,
      Key: {
        PK: `USER#${id}`,
        SK: 'PROFILE',
      },
    });

    await this.client.send(command);
  }

  async createGuest(userId: string, email: string): Promise<void> {
    const now = new Date().toISOString();

    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: {
        PK: `USER#${userId}`,
        SK: 'PROFILE',
        email,
        createdAt: now,
        updatedAt: now,
      },
      ConditionExpression: 'attribute_not_exists(PK)',
    });

    await this.client.send(command);
  }
}
