import { PineconeMetadata, PineconeRepository } from '../repositories/pinecone-repository';
import { PerplexityRepository } from '../repositories/perplexity-repository';
import { AgentContext, AgentRequest, AgentResponse } from '@shared/types/agent';
import dedent from 'dedent';
import { ScoredPineconeRecord } from '@pinecone-database/pinecone';
import { trimToJsonArray } from '../utils/utils';

export interface AgentOptions {
  scoreThreshold: number;
}

type ExtractTextResponse = Array<{ chunk: string }>;

export class AgentService {
  constructor(
    private readonly pineconeRepository: PineconeRepository,
    private readonly perplexityRepository: PerplexityRepository,
    private readonly options: AgentOptions
  ) {}

  async ask(userId: string, request: AgentRequest): Promise<AgentResponse> {
    console.log('Query:', request.prompt);

    const results = await this.pineconeRepository.queryEmbedding(request.prompt, userId, request.tripId);
    console.log(
      'Results:',
      JSON.stringify(
        results.map((result) => ({
          score: result.score,
          documentName: result.metadata?.documentName,
          text: result.metadata?.text,
        })),
        null,
        2
      )
    );

    // Separate high and low matches
    const { highMatches, lowMatches } = results
      .filter((result) => result.score && result.score > 0.3)
      .sort((a, b) => b.score! - a.score!)
      .reduce(
        (acc, result) => {
          // Only add if we haven't reached our limit of 3
          if (result.score! >= this.options.scoreThreshold && acc.highMatches.length < 3) {
            acc.highMatches.push(result);
          } else if (result.score! < this.options.scoreThreshold && acc.lowMatches.length < 3) {
            acc.lowMatches.push(result);
          }
          return acc;
        },
        {
          highMatches: [] as ScoredPineconeRecord<PineconeMetadata>[],
          lowMatches: [] as ScoredPineconeRecord<PineconeMetadata>[],
        }
      );

    const prompt = dedent`
      You are Tripppy, an AI assistant that helps users with their itineraries.

      ${
        highMatches.length > 0
          ? highMatches.map((match) => match.metadata?.text).join('\n\n')
          : lowMatches.length > 0
          ? lowMatches.map((match) => match.metadata?.text).join('\n\n') +
            'Take the previous information into account and search the internet for the following question.'
          : ''
      }

      ${request.prompt}
    `;

    console.log('Perplexity search:', JSON.stringify(prompt, null, 2));

    const response = await this.perplexityRepository.ask(prompt);

    return {
      content: response.text,
      sources: response.sources,
      contexts: this.getContexts(highMatches),
    };
  }

  async extractText(content: string): Promise<string[]> {
    console.log('zlatko: extractText', content);

    const prompt = dedent`
      Given a text, clean and remove any unnecessary formatting.

      1. Process all text elements (headers, footers, body, disclaimers) from the input.
      2. Extract ALL explicit data points including but not limited to:
        - Codes (confirmation/PIN/license numbers)
        - Monetary values with currency symbols
        - Dates/times with time zones
        - Addresses with postal codes
        - Contact details (email/phone)
        - Names (guest/property)
        - Room types/occupancy
        - Policies (cancellation/check-in/age restrictions)
        - Payment methods
        - Fees/taxes
        - COVID measures
        - Security warnings
      3. Split into logical chunks by:
        - Keeping related policy details together
        - Separating distinct categories (e.g., pricing vs. contact info)
        - Preserving sequential order of information
        - Making single-sentence chunks for isolated details
      4. Never omit numbers, codes, or specific policy phrases.
      5. Maintain original terminology

      Present the output as a valid JSON array of objects, where each object has a 'chunk' key containing the grouped text.
      Do NOT include any explanations, extra text, or markdown formatting—return only the JSON output.
      Format:
      [
        {
          "chunk": "First meaningful chunk of text."
        },
        {
          "chunk": "Second meaningful chunk of text."
        }
      ]


      ${content}
  `;

    // const prompt = dedent`
    //   Given a text, clean and remove any unnecessary formatting.
    //   Extract all human-readable content, including every explicit piece of information such as names, dates, addresses, numbers, and any other identifiable details.
    //   Break the text into logical chunks, ensuring each chunk is coherent, meaningful, and groups related sentences or data together.
    //   If a piece of information is self-contained, it can be a single sentence.
    //   Present the output as a valid JSON array of objects, where each object has a 'chunk' key containing the grouped text.
    //   Do NOT include any explanations, extra text, or markdown formatting—return only the JSON output.
    //   Format:
    //   [
    //     {
    //       "chunk": "First meaningful chunk of text."
    //     },
    //     {
    //       "chunk": "Second meaningful chunk of text."
    //     }
    //   ]

    //   ${content}
    // `;

    const response = await this.perplexityRepository.ask(prompt);
    console.log("Perplexity's response:", JSON.stringify(response.text, null, 2));

    const text = trimToJsonArray(response.text);
    const result = JSON.parse(text) as ExtractTextResponse;

    return result.map((item) => item.chunk);
  }

  private getContexts(matches: ScoredPineconeRecord<PineconeMetadata>[]): AgentContext[] {
    const contextsMatched = matches
      .reduce((acc, match) => {
        const tripId = match.metadata?.tripId || '';
        const documentId = match.metadata?.documentId || '';
        const documentName = match.metadata?.documentName || '';
        const score = match.score || 0;

        if (acc.has(documentId)) {
          const doc = acc.get(documentId)!;

          acc.set(documentId, {
            ...doc,
            score: Math.max(doc.score, score),
          });
        } else {
          acc.set(documentId, {
            tripId,
            documentId,
            documentName,
            score,
          });
        }

        return acc;
      }, new Map<string, AgentContext>())
      .values();

    return Array.from(contextsMatched);
  }
}
