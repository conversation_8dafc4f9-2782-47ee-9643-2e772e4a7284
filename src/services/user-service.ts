import { UserRepository } from '../repositories/user-repository';
import {
  CognitoIdentityProviderClient,
  AdminGetUserCommand,
  AdminCreateUserCommand,
  AdminCreateUserCommandInput,
  AdminCreateUserCommandOutput,
  ListUsersCommand,
} from '@aws-sdk/client-cognito-identity-provider';

const getUserId = (response: AdminCreateUserCommandOutput): string | undefined => {
  return response.User?.Attributes?.find((attr) => attr.Name === 'sub')?.Value;
};

export class UserService {
  private readonly cognitoClient: CognitoIdentityProviderClient;

  constructor(
    private readonly userPoolId: string,
    private readonly userRepository: UserRepository,
  ) {
    this.cognitoClient = new CognitoIdentityProviderClient();
  }

  // FIXME this is the original method which should deal with emails as username (however, doesn't work with Google sign-ins)
  //   async getUser(email: string): Promise<string | undefined> {
  //     try {
  //       const command = new AdminGetUserCommand({
  //         UserPoolId: this.userPoolId,
  //         Username: email,
  //       });

  //       const response = await this.cognitoClient.send(command);
  //       return getUserId(response);
  //     } catch (error) {
  //       if (error instanceof Error && 'name' in error && error.name === 'UserNotFoundException') {
  //         return undefined;
  //       }
  //       throw error;
  //     }
  //   }

  // TODO This is non-peformant solution, but it works for now (replace it with the one above)
  async getUser(email: string): Promise<string | undefined> {
    const command = new ListUsersCommand({
      UserPoolId: this.userPoolId,
      Filter: `email = "${email}"`,
    });

    const response = await this.cognitoClient.send(command);
    if (!response?.Users?.length) {
      return undefined;
    }

    return response.Users[0].Attributes?.find((attr) => attr.Name === 'sub')?.Value;
  }

  async createGuest(email: string): Promise<string | undefined> {
    const params: AdminCreateUserCommandInput = {
      UserPoolId: this.userPoolId,
      Username: email,
      UserAttributes: [
        {
          Name: 'email',
          Value: email,
        },
        {
          Name: 'email_verified',
          Value: 'false',
        },
      ],
      MessageAction: 'SUPPRESS', // Suppress welcome email
    };

    try {
      const command = new AdminCreateUserCommand(params);
      const response = await this.cognitoClient.send(command);
      const userId = getUserId(response);

      if (userId) {
        await this.userRepository.createGuest(userId, email);
      }

      return userId;
    } catch (error) {
      console.error('Error creating guest in Cognito:', error);
      throw error;
    }
  }
}
