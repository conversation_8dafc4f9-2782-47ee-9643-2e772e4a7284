import { TripDocumentMetadata } from '@shared/types/trip';
import { PineconeRepository } from '../repositories/pinecone-repository';
import { S3Repository } from '../repositories/s3-repository';
import { generateBucketKey } from '../utils/utils';
import { AgentService } from './agent-service';

export interface TripDocument extends TripDocumentMetadata {
  content: Buffer;
}

export class DocumentService {
  constructor(
    private readonly s3Repository: S3Repository,
    private readonly pineconeRepository: PineconeRepository,
    private readonly agentService: AgentService
  ) {}

  async saveDocuments(userId: string, tripId: string, documents: TripDocumentMetadata[] | TripDocument[]) {
    if (!documents?.length) return;

    return await Promise.all(
      documents.map(async (document) => {
        try {
          const filename = generateBucketKey(userId, tripId, document.id);

          let content: Buffer;

          if ('content' in document && document.content) {
            content = document.content;
            await this.s3Repository.saveFile(filename, content, document.contentType!);
          } else {
            content = await this.s3Repository.getFile(filename);
            await this.s3Repository.confirmFile(filename);
          }

          // Save document to Pinecone
          const chunks = await this.agentService.extractText(content.toString());
          await this.pineconeRepository.upsertEmbeddings(userId, tripId, document, chunks);
        } catch (e) {
          console.error('Error saving document:', e);
        }
      })
    );
  }

  async deleteDocuments(userId: string, tripId: string, documents?: TripDocumentMetadata[]) {
    if (!documents?.length) return;

    const documentIds = documents.map((document) => document.id);
    await this.pineconeRepository.deleteEmbeddings(userId, tripId, documentIds);

    const filenames = documentIds.map((documentId: string) => generateBucketKey(userId, tripId, documentId));
    await this.s3Repository.deleteFiles(filenames);
  }
}
