import { Attachment, simpleParser } from 'mailparser';
import { S3Repository } from '../repositories/s3-repository';
import { htmlToText } from 'html-to-text';

export interface Email {
  filename: string;
  from: string | null;
  subject: string;
  date: string;
  text: string;
  html: string;
  content: string;
  messageId: string;
  headers: object;
  attachments: Attachment[];
}

export class EmailService {
  constructor(private readonly s3Repository: S3Repository) {}

  public async processEmail(filename: string): Promise<Email> {
    const file = await this.s3Repository.getFile(filename);
    const email = await this.parseEmail(filename, file);
    return email;
  }

  private async parseEmail(filename: string, content: Buffer): Promise<Email> {
    try {
      const parsed = await simpleParser(content);

      const email: Email = {
        filename,
        from: this.extractEmail(parsed.from?.text || ''),
        subject: parsed.subject || '',
        date: parsed.date?.toISOString() || '',
        text: parsed.text || '',
        html: parsed.html || '',
        content: parsed.text || htmlToText(parsed.html || ''),
        messageId: parsed.messageId || '',
        headers: parsed.headers || {},
        attachments: parsed.attachments.filter((attachment) => attachment.contentDisposition !== 'inline'),
      };

      return email;
    } catch (error) {
      console.error('Error parsing email:', error);
      throw new Error('Failed to parse the email.');
    }
  }

  private extractEmail(input: string): string | null {
    const emailRegex = /(?:[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,})|(?:".+" <([^>]+)>)/;
    const match = input.match(emailRegex);
    return match ? match[1] || match[0] : null;
  }
}
