import { Trip, TripDocumentMetadata } from '@shared/types/trip';
import { Trip<PERSON>eriod, TripRepository } from '../repositories/trip-repository';
import { S3Repository } from '../repositories/s3-repository';
import { PineconeRepository } from '../repositories/pinecone-repository';
import { DocumentService, TripDocument } from './document-service';
import { AgentService } from './agent-service';

export class TripService {
  private readonly documentService: DocumentService;

  constructor(
    private readonly tripRepository: TripRepository,
    readonly s3Repository: S3Repository,
    readonly pineconeRepository: PineconeRepository,
    readonly agentService: AgentService
  ) {
    this.documentService = new DocumentService(s3Repository, pineconeRepository, agentService);
  }

  async saveTrip(userId: string, trip: Trip, isEdit: boolean) {
    const documentsToAdd: TripDocumentMetadata[] = [];
    const documentsToRemove: TripDocumentMetadata[] = [];

    if (isEdit) {
      const attributes = await this.tripRepository.updateTrip(userId, trip);
      documentsToAdd.push(...this.findNewDocuments(trip.documents, attributes?.documents));
      documentsToRemove.push(...this.findNewDocuments(attributes?.documents, trip.documents));
    } else {
      await this.tripRepository.createTrip(userId, trip);
      documentsToAdd.push(...(trip.documents || []));
    }

    if (documentsToAdd.length) {
      await this.documentService.saveDocuments(userId, trip.id, documentsToAdd);
    }

    if (documentsToRemove.length) {
      await this.documentService.deleteDocuments(userId, trip.id, documentsToRemove);
    }
  }

  async getTrips(userId: string, period: TripPeriod): Promise<Trip[]> {
    return this.tripRepository.getTrips(userId, period);
  }

  async createDefaultTrip(userId: string, documents: TripDocument[]): Promise<string> {
    const tripId = crypto.randomUUID();

    const trip: Trip = {
      id: tripId,
      name: 'My upcoming trip',
      documents: this.stripContent(documents),
    };

    await this.tripRepository.createTrip(userId, trip);
    if (documents.length) {
      await this.documentService.saveDocuments(userId, tripId, documents);
    }

    return tripId;
  }

  async getUpcomingTrip(userId: string): Promise<Trip | null> {
    const trips = await this.tripRepository.getTrips(userId, TripPeriod.UPCOMING);
    return trips.length > 0 ? trips[0] : null;
  }

  async deleteTrip(userId: string, tripId: string) {
    const trip = await this.tripRepository.deleteTrip(userId, tripId);

    const documents: TripDocumentMetadata[] = trip.Attributes?.documents;
    if (documents?.length) {
      await this.documentService.deleteDocuments(userId, tripId, documents);
    }
  }

  async appendDocuments(userId: string, tripId: string, documents: TripDocument[]) {
    const documentsField: keyof Trip = 'documents';
    await this.tripRepository.appendToArrayAttribute(userId, tripId, documentsField, this.stripContent(documents));
    await this.documentService.saveDocuments(userId, tripId, documents);
  }

  private findNewDocuments(
    currentDocuments: TripDocumentMetadata[] | undefined,
    previousDocuments: TripDocumentMetadata[] | undefined
  ): TripDocumentMetadata[] {
    if (!currentDocuments) return [];
    if (!previousDocuments) return currentDocuments;

    return currentDocuments.filter((current) => !previousDocuments.some((prev) => prev.id === current.id));
  }

  stripContent(documents: TripDocument[]): TripDocumentMetadata[] {
    return documents.map(({ content, ...rest }) => rest);
  }
}
