import { Command } from '../lib/pipeliner';
import { UserService } from '../services/user-service';

export class UserFetcher implements Command<string, string | undefined> {
  constructor(private readonly userService: UserService) {}

  async execute(emailFrom: string): Promise<string | undefined> {
    try {
      const userId = await this.userService.getUser(emailFrom);
      return userId;
    } catch (error) {
      throw error;
    }
  }
}
