import { TripRepository } from '../repositories/trip-repository';
import { Command, CommandOptions } from '../lib/pipeliner';
import { Trip } from '@shared/types/trip';
import { NewEmailContext } from './new-email-context';
import { TripPeriod } from '../repositories/trip-repository';

export class <PERSON>Fetcher implements Command<string, Trip, NewEmailContext> {
  constructor(
    private readonly tripRepository: TripRepository,
    private readonly tripPeriod: TripPeriod,
  ) {}

  async execute(input: string, options?: CommandOptions<NewEmailContext>): Promise<Trip> {
    const userId = options!.context!.userId;
    const trips = await this.tripRepository.getTrips(userId, this.tripPeriod);
    return trips[0];
  }
}
