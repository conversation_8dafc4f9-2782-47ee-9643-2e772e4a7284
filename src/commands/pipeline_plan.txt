const record = event.Records[0];
const incomingEmailFilename = record.s3.object.key;

const context: MyContext = { 
    userId: string;
    tripId: string;
    documents: [];
    guestUser: boolean;
};

const pipeline = new PipelineBuilder<string, MyContext>()
    .pipe(new EmailProcessor())
    .pipe(new UserFetcher())
    .branch(
      (user) => user !== null,
      (thenBuilder) => thenBuilder.pipe(new PassThrough()),
      (elseBuilder) => elseBuilder
        .pipe(new UserCreator({guest: true}))
    )
    .branch(
      (user, options) => options.context.guestUser,
      (thenBuilder) => thenBuilder.pipe(new TripCreator({default: true})),
      (elseBuilder) => elseBuilder
        .pipe(new TripFetcher({upcoming: true}))
        .branch(
            (trip) => trip !== null,
            (thenBuilder) => thenBuilder.pipe(new DocumentAppender()),
            (elseBuilder) => elseBuilder
                .pipe(new TripFetcher({default: true}))
                .branch(
                    (defaultTrip) => defaultTrip !== null,
                    (thenBuilder) => thenBuilder.pipe(new DocumentAppender()),
                    (elseBuilder) => elseBuilder.pipe(new TripCreator({default: true}))
                )
        )
    )
    .run(incomingEmailFilename, context)

