import { Command } from '../lib/pipeliner';
import { UserService } from '../services/user-service';

export class User<PERSON><PERSON> implements Command<string, string> {
  constructor(private readonly userService: UserService) {}

  async execute(emailFrom: string): Promise<string> {
    try {
      const userId = await this.userService.createGuest(emailFrom);
      return userId!;
    } catch (error) {
      throw error;
    }
  }
}
