import { S3Repository } from '../repositories/s3-repository';
import { Command } from '../lib/pipeliner';
import { Email, EmailService } from '../services/email-service';

export class EmailParser implements Command<string, string> {
  constructor(private readonly s3Repository: S3Repository) {}

  async execute(s3Filename: string): Promise<string> {
    const emailService = new EmailService(this.s3Repository);
    const email: Email = await emailService.processEmail(s3Filename);

    if (!email.from) {
      throw new Error('email.from cannot be extracted!');
    }

    return email.from;
  }
}
