import {
  AdminLinkProviderForUserCommand,
  CognitoIdentityProviderClient,
  AdminLinkProviderForUserRequest,
  AdminGetUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';

import { PreSignUpTriggerEvent, Handler } from 'aws-lambda';

const client = new CognitoIdentityProviderClient(); // Replace with your region

const getUser = async (userPoolId: string, username: string) => {
  const params = {
    UserPoolId: userPoolId,
    Username: username,
  };

  const command = new AdminGetUserCommand(params);

  try {
    const data = await client.send(command);
    return data;
  } catch (error) {
    return null;
  }
};

export const handler: Handler = async (event: PreSignUpTriggerEvent) => {
  if (event.triggerSource === 'PreSignUp_ExternalProvider') {
    const { userPoolId, request } = event;
    const { userAttributes } = request;
    const email = userAttributes.email;

    const user = await getUser(userPoolId, email);

    if (user) {
      // Link the new federated user to the existing user
      const identities = JSON.parse(request.userAttributes.identities);

      const params: AdminLinkProviderForUserRequest = {
        UserPoolId: userPoolId,
        DestinationUser: {
          ProviderName: 'Cognito',
          ProviderAttributeValue: user.Username,
        },
        SourceUser: {
          ProviderName: identities[0].providerName,
          ProviderAttributeName: 'Cognito_Subject',
          ProviderAttributeValue: user.Username,
        },
      };

      const command = new AdminLinkProviderForUserCommand(params);

      try {
        await client.send(command);
      } catch (error) {
        throw error;
      }
    }
  }

  return event;
};
