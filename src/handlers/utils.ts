import { APIGatewayProxyEvent } from 'aws-lambda';

export const getUserId = (event: APIGatewayProxyEvent) => event.requestContext.authorizer?.claims?.sub;

export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export const response = <T>(statusCode: number, payload?: T) => {
  return {
    statusCode,
    headers: DEFAULT_HEADERS,
    body: payload ? JSON.stringify(payload) : '',
  };
};

export const ok = <T>(payload?: T) => response(200, payload);

export const created = <T>(payload?: T) => response(201, payload);

export const forbidden = <T>(payload?: T) => response(403, payload);

export const notFound = <T>(payload?: T) => response(404, payload);

export const error = (message: string | unknown, statusCode = 500) => response(statusCode, { error: message });

export const deleted = () => response(204);

export const download = (fileName: string, fileContent: Buffer<ArrayBufferLike>, contentType?: string) => {
  return {
    statusCode: 200,
    headers: {
      'Content-Type': contentType || 'application/octet-stream',
      'Content-Disposition': `attachment; filename="${fileName}"`,
      'Content-Length': fileContent.length.toString(),
    },
    body: fileContent.toString('base64'),
    isBase64Encoded: true,
  };
};
