import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { error, getUserId, ok } from '../utils';
import { S3Client } from '@aws-sdk/client-s3';
import { S3Repository } from '../../repositories/s3-repository';
import { generateBucketKey } from '../../utils/utils';

const s3Repository = new S3Repository(new S3Client());

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const userId = getUserId(event);
  const { tripId, documentIds } = JSON.parse(event.body || '{}');

  const filenames = documentIds.map((documentId: string) => generateBucketKey(userId, tripId, documentId));

  try {
    await s3Repository.deleteFiles(filenames);
    return ok();
  } catch (e) {
    return error('Failed to delete documents');
  }
};
