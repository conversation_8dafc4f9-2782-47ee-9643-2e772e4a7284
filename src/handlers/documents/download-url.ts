import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { S3Client } from '@aws-sdk/client-s3';
import { S3Repository } from '../../repositories/s3-repository';
import { getUserId, ok, error } from '../utils';
import { generateBucketKey } from '../../utils/utils';

const s3Repository = new S3Repository(new S3Client());

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const userId = getUserId(event);

    const { tripId, documentId } = event.pathParameters || {};
    if (!tripId || !documentId) {
      return error('documentId and tripId are required', 400);
    }

    const filename = generateBucketKey(userId, tripId, documentId);

    const downloadUrl = await s3Repository.getDownloadUrl(filename);

    return ok(downloadUrl);
  } catch (e) {
    return error('Failed to generate download URL');
  }
};
