import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { TripRepository, TripPeriod } from '../../repositories/trip-repository';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { getUserId, ok } from '../utils';

const tripRepository = new TripRepository(DynamoDBDocumentClient.from(new DynamoDBClient()));

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const userId = getUserId(event);
  const tripId = event.pathParameters?.tripId;

  if (tripId) {
    const trip = await tripRepository.getTrip(userId, tripId);
    return ok(trip);
  } else {
    const type = event.queryStringParameters?.type as keyof typeof TripPeriod | undefined;
    const trips = await tripRepository.getTrips(userId, type && TripPeriod[type]);
    return ok(trips);
  }
};
