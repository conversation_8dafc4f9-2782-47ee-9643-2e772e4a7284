import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { TripRepository } from '../../repositories/trip-repository';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { getUserId, error, deleted } from '../utils';
import { TripService } from '../../services/trip-service';
import { S3Client } from '@aws-sdk/client-s3';
import { S3Repository } from '../../repositories/s3-repository';
import { PineconeRepository } from '../../repositories/pinecone-repository';

const tripService = new TripService(
  new TripRepository(DynamoDBDocumentClient.from(new DynamoDBClient())),
  new S3Repository(new S3Client()),
  new PineconeRepository(process.env.PINECONE_API_KEY!, process.env.OPENAI_API_KEY!)
);

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const userId = getUserId(event);
  const tripId = event.pathParameters?.tripId;

  if (!tripId) {
    return error('Trip ID is required', 400);
  }

  await tripService.deleteTrip(userId, tripId!);
  return deleted();
};
