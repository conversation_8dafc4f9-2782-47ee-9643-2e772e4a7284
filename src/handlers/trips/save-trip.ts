import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { Trip } from '@shared/types/trip';
import { S3Client } from '@aws-sdk/client-s3';
import { TripRepository } from '../../repositories/trip-repository';
import { PineconeRepository } from '../../repositories/pinecone-repository';
import { S3Repository } from '../../repositories/s3-repository';
import { TripService } from '../../services/trip-service';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { getUserId, ok, error, created } from '../utils';

const tripService = new TripService(
  new TripRepository(DynamoDBDocumentClient.from(new DynamoDBClient())),
  new S3Repository(new S3Client()),
  new PineconeRepository(process.env.PINECONE_API_KEY!, process.env.OPENAI_API_KEY!, {
    topK: Number(process.env.TOP_K!),
  })
);

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const trip = JSON.parse(event.body || '{}') as Trip;
  const isEdit = event.httpMethod === 'PUT';

  try {
    await tripService.saveTrip(getUserId(event), trip, isEdit);
    return isEdit ? ok() : created();
  } catch (e) {
    return error(e as any);
  }
};
