import { AgentRequest } from '@shared/types/agent';
import { getUserId } from '../utils';
import { PerplexityRepository } from '../../repositories/perplexity-repository';
import { PineconeRepository } from '../../repositories/pinecone-repository';
import { AgentService } from '../../services/agent-service';

// Add type declaration for awslambda
declare const awslambda: {
  streamifyResponse<T>(
    handler: (event: AgentRequest, responseStream: ResponseStream, context: StreamingContext) => Promise<void>
  ): (event: AgentRequest) => Promise<T>;
};

interface ResponseStream {
  setContentType: (contentType: string) => void;
  write: (chunk: string) => void;
  end: () => void;
}

type StreamingContext = {
  http: {
    method: string;
    path: string;
    protocol: string;
    headers: Record<string, string>;
    queryString: Record<string, string>;
    sourceIp: string;
  };
};

const agentService = new AgentService(
  new PineconeRepository(process.env.PINECONE_API_KEY!, process.env.OPENAI_API_KEY!),
  new PerplexityRepository(process.env.PERPLEXITY_API_KEY!)
);

async function* streamResponse(userId: string, request: AgentRequest) {
  // const response = await agentService.ask(userId, request);
  // for await (const chunk of response) {
  //   const text = chunk.choices[0]?.delta?.content ?? '';
  //   console.log(`zlatko:data: ${JSON.stringify({ text })}\n\n`);
  //   yield `data: ${JSON.stringify({ text })}\n\n`;
  // }
}

export const handler = awslambda.streamifyResponse(async (event, responseStream, context) => {
  // const userId = getUserId(event);
  const userId = '045834f8-a011-7058-8dcc-0fdbaf2da6ef';

  responseStream.setContentType('text/event-stream');

  const request: AgentRequest = {
    tripId: '1f9b00a5-1d47-482d-a741-8d1742b8d0e4',
    query: 'Where is Paris?',
  };

  for await (const chunk of streamResponse(userId, request)) {
    responseStream.write(chunk);
  }

  // const mockResponse = ['Zlatko, ', 'Goran', 'Vesna', 'Ljubomir'];

  // for (const text of mockResponse) {
  //   responseStream.write(`data: ${JSON.stringify({ text })}\n\n`);
  //   await new Promise((resolve) => setTimeout(resolve, 1000));
  // }

  responseStream.end();
});
