import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ok } from '../utils';
import { AgentService } from 'src/services/agent-service';
import { PineconeRepository } from '../../repositories/pinecone-repository';
import { PerplexityRepository } from '../../repositories/perplexity-repository';
import { AgentRequest, AgentResponse } from '@shared/types/agent';
import { TripService } from '../../services/trip-service';
import { TripRepository } from '../../repositories/trip-repository';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { S3Repository } from 'src/repositories/s3-repository';
import { S3Client } from '@aws-sdk/client-s3';
import telegramifyMarkdown from 'telegramify-markdown';

const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;
const TELEGRAM_API_URL = `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}`;

const pineconeRepository = new PineconeRepository(process.env.PINECONE_API_KEY!, process.env.OPENAI_API_KEY!, {
  topK: Number(process.env.TOP_K!),
});

const agentService = new AgentService(pineconeRepository, new PerplexityRepository(process.env.PERPLEXITY_API_KEY!), {
  scoreThreshold: Number(process.env.SCORE_THRESHOLD!),
});

const tripService = new TripService(
  new TripRepository(DynamoDBDocumentClient.from(new DynamoDBClient())),
  new S3Repository(new S3Client()),
  pineconeRepository,
  agentService
);

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const body = JSON.parse(event.body || '{}');
  const message = body.message;

  if (!message || !message.text) {
    return ok({ message: 'No text message found' });
  }

  const chatId = message.chat.id;
  const prompt = message.text;

  await startTyping(chatId);

  const userId = '045834f8-a011-7058-8dcc-0fdbaf2da6ef'; //FIXME: getUserId(event);
  const upcomingTrip = await tripService.getUpcomingTrip(userId);
  const request: AgentRequest = {
    tripId: upcomingTrip?.id ?? null,
    prompt,
  };

  const response = await agentService.ask(userId, request);

  await sendMessage(chatId, response);

  return ok();
};

const startTyping = async (chatId: string) => {
  return fetch(`${TELEGRAM_API_URL}/sendChatAction`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      chat_id: chatId,
      action: 'typing',
    }),
  });
};

const sendMessage = async (chatId: string, response: AgentResponse) => {
  const shortenText = (text: string) => (text.length > 30 ? text.slice(0, 27) + '...' : text);

  const sources = response.contexts
    ?.sort((a, b) => b.score - a.score)
    .map((context) => '• ' + shortenText(context.documentName))
    .join('\n');

  const text = `${response.content}${sources ? `\n────\nSources:\n${sources}` : ''}`;

  return fetch(`${TELEGRAM_API_URL}/sendMessage`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      chat_id: chatId,
      text: telegramifyMarkdown(text, 'keep'),
      parse_mode: 'MarkdownV2',
    }),
  });
};
