import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { PerplexityRepository } from '../../repositories/perplexity-repository';
import { PineconeRepository } from '../../repositories/pinecone-repository';
import { AgentService } from '../../services/agent-service';
import { getUserId, ok } from '../utils';
import { AgentRequest } from '@shared/types/agent';

const agentService = new AgentService(
  new PineconeRepository(process.env.PINECONE_API_KEY!, process.env.OPENAI_API_KEY!, {
    topK: Number(process.env.TOP_K!),
  }),
  new PerplexityRepository(process.env.PERPLEXITY_API_KEY!),
  {
    scoreThreshold: Number(process.env.SCORE_THRESHOLD!),
  }
);

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const request: AgentRequest = JSON.parse(event.body!);
  const response = await agentService.ask(getUserId(event), request);
  return ok(response);
};
