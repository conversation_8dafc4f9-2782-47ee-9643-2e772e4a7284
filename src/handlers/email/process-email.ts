import { S3Event } from 'aws-lambda';
import { EmailService, Email } from '../../services/email-service';
import { S3Repository } from '../../repositories/s3-repository';
import { S3Client } from '@aws-sdk/client-s3';
import { UserService } from '../../services/user-service';
import { UserRepository } from '../../repositories/user-repository';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { TripRepository, TripPeriod } from '../../repositories/trip-repository';
import { PineconeRepository } from '../../repositories/pinecone-repository';
import { TripService } from '../../services/trip-service';
import { TripDocument } from '../../services/document-service';
import { Attachment } from 'mailparser';
import { AgentService } from '../../services/agent-service';
import { PerplexityRepository } from '../../repositories/perplexity-repository';

const s3Repository = new S3Repository(new S3Client());
const emailService = new EmailService(s3Repository);
const dynamoDBDocumentClient = DynamoDBDocumentClient.from(new DynamoDBClient());
const userService = new UserService(process.env.USER_POOL_ID!, new UserRepository(dynamoDBDocumentClient));

const pineconeRepository = new PineconeRepository(process.env.PINECONE_API_KEY!, process.env.OPENAI_API_KEY!, {
  topK: Number(process.env.TOP_K!),
});

const agentService = new AgentService(pineconeRepository, new PerplexityRepository(process.env.PERPLEXITY_API_KEY!), {
  scoreThreshold: Number(process.env.SCORE_THRESHOLD!),
});

const tripService = new TripService(
  new TripRepository(dynamoDBDocumentClient),
  s3Repository,
  pineconeRepository,
  agentService
);

export const handler = async (event: S3Event): Promise<void> => {
  if (!event.Records.length) return;

  const record = event.Records[0];
  const incomingEmailFilename = record.s3.object.key;

  const email: Email = await emailService.processEmail(incomingEmailFilename);
  if (!email.from) {
    console.error('Invalid email received!');
    return;
  }

  const { userId, isGuest } = await getOrCreateUser(email.from);

  const emailId = crypto.randomUUID();
  const documents = getEmailWithAttachmentsAsDocuments(emailId, email);

  // FIXME: The document handling is incorrect (?)
  const { tripId, isDefault } = await getOrCreateTrip(userId, isGuest, documents);

  // Send email to user to inform about successful trip upload. If user not existing, send a link for registration.
  // Otherwise, send a link to the trip.
};

const getOrCreateUser = async (emailFrom: string): Promise<{ userId: string; isGuest: boolean }> => {
  const existingUserId = await userService.getUser(emailFrom);

  const { userId, isGuest } = existingUserId
    ? { userId: existingUserId, isGuest: false }
    : { userId: await userService.createGuest(emailFrom), isGuest: true };

  if (!userId) {
    throw new Error('Failed to get or create user!');
  }

  return { userId, isGuest };
};

const getOrCreateTrip = async (
  userId: string,
  isGuest: boolean,
  documents: TripDocument[]
): Promise<{ tripId: string; isDefault: boolean }> => {
  if (isGuest) {
    // Create a DEFAULT trip since the user is a guest and this is their first trip for sure
    const tripId = await tripService.createDefaultTrip(userId, documents);
    return { tripId, isDefault: true };
  } else {
    // Check if they have an upcoming trip soon
    const upcomingTrip = await tripService.getUpcomingTrip(userId);
    if (upcomingTrip) {
      await tripService.appendDocuments(userId, upcomingTrip.id, documents);
      return { tripId: upcomingTrip.id, isDefault: false };
    } else {
      // Check if they have a DEFAULT trip
      const defaultTrips = await tripService.getTrips(userId, TripPeriod.DEFAULT);
      if (defaultTrips.length) {
        const defaultTrip = defaultTrips[0];
        await tripService.appendDocuments(userId, defaultTrip.id, documents);
        return { tripId: defaultTrip.id, isDefault: true };
      } else {
        const tripId = await tripService.createDefaultTrip(userId, documents);
        return { tripId, isDefault: true };
      }
    }
  }
};

const getEmailWithAttachmentsAsDocuments = (emailId: string, email: Email): TripDocument[] => {
  const isSupportedAttachment = (attachment: Attachment) =>
    ['text/plain', 'text/html', 'application/pdf'].includes(attachment.contentType);

  return [
    {
      id: emailId,
      parentId: email.filename.split('/').pop() || email.filename,
      name: email.subject,
      contentType: 'message/rfc822',
      content: Buffer.from(email.content),
    },
    ...email.attachments.filter(isSupportedAttachment).map((attachment) => ({
      id: crypto.randomUUID(),
      parentId: emailId,
      name: attachment.filename,
      size: attachment.size,
      contentType: attachment.contentType,
      content: attachment.content,
    })),
  ];
};
