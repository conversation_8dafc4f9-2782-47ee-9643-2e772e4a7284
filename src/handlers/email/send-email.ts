import { SES } from 'aws-sdk';

const ses = new SES();

export interface EmailTemplate {
  templateName: string;
  toAddress: string;
  fromAddress: string;
  templateData: Record<string, string>;
}

export const handler = async (event: EmailTemplate): Promise<void> => {
  const { templateName, toAddress, fromAddress, templateData } = event;

  try {
    await ses
      .sendTemplatedEmail({
        Destination: {
          ToAddresses: [toAddress],
        },
        Source: fromAddress,
        Template: templateName,
        TemplateData: JSON.stringify(templateData),
      })
      .promise();

    console.log(`Email sent using template ${templateName} to ${toAddress}`);
  } catch (error) {
    console.error('Failed to send templated email:', error);
    throw error;
  }
};
