{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "server": "json-server --watch db.json --port 3000"}, "dependencies": {"@aws-amplify/auth": "^6.10.2", "@aws-amplify/ui-react": "^6.9.1", "@mantine/core": "7.16.2", "@mantine/dates": "7.16.2", "@mantine/form": "7.16.2", "@mantine/hooks": "7.16.2", "@mantine/modals": "7.16.2", "@mantine/notifications": "7.16.2", "@tabler/icons-react": "^3.29.0", "aws-amplify": "^6.13.1", "axios": "^1.7.9", "clsx": "^2.1.1", "openai": "^4.98.0", "p-limit": "^6.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.1.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "json-server": "^1.0.0-beta.3", "postcss": "^8.5.1", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.4.2", "sass-embedded": "^1.83.4", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}