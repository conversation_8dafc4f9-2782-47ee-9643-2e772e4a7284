import { BrowserRouter, Route, Routes } from 'react-router-dom';
import { Header } from './components/Header';
import { Trips } from './pages/trips/Trips';
import { Contact } from './pages/Contact';
import { AgentPage } from './pages/agent/AgentPage';
import { Home } from './pages/Home';
import { AuthenticationForm } from './pages/AuthenticationForm';
import { Footer } from './components/Footer';
import HolyGrailLayout from './components/MainLayout';
import { NewTripPage } from './pages/trips/new-trip/NewTripPage';
import { ProtectedRoute } from './components/ProtectedRoute';

export const App = () => {
  return (
    <BrowserRouter>
      <HolyGrailLayout
        header={<Header />}
        footer={<Footer />}
        main={
          <Routes>
            <Route index element={<Home />} />
            <Route path="trips">
              <Route index element={
                <ProtectedRoute>
                  <Trips />
                </ProtectedRoute>
              } />
              <Route path="edit" element={
                <ProtectedRoute>
                  <NewTripPage />
                </ProtectedRoute>
              } />
            </Route>
            <Route
              path="/agent"
              element={
                <ProtectedRoute>
                  <AgentPage />
                </ProtectedRoute>
              }
            />
            <Route path="/contact" element={<Contact />} />
            <Route path="/signin" element={<AuthenticationForm key="login" type="login" />} />
            <Route path="/signup" element={<AuthenticationForm key="register" type="register" />} />
          </Routes>
        }
      />
    </BrowserRouter>
  );
};
