@keyframes gradientMoveLeft {
  0% {
    background-position: 200% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animatedText {
  font-size: 12px;
  font-weight: bold;
  background-image: linear-gradient(90deg, #333333, #aaa, #333333);
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: gradientMoveLeft 1.5s infinite linear;
}

@keyframes pulse {
  0% {
    transform: scale(0.5);
    opacity: 0.5;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.5);
    opacity: 0.5;
  }
}

.pulsingDot {
  width: 8px;
  height: 8px;
  background-color: var(--mantine-color-gray-6);
  border-radius: 50%;
  animation: pulse 1s infinite ease-in-out;
}
