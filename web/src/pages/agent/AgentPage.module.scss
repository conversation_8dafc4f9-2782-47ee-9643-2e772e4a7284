.container {
  height: calc(100vh - 260px); // Accounting for header (80px) and footer (60px) and padding (120px)
}

.topSection {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;

  &::-webkit-scrollbar {
    width: 8px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--mantine-color-gray-3);
    border-radius: 4px;

    &:hover {
      background-color: var(--mantine-color-gray-4);
    }
  }

  // Firefox
  scrollbar-width: thin;
  scrollbar-color: var(--mantine-color-gray-3) transparent;
}

.bottomSection {
  flex: 0 0 auto; // Don't grow or shrink
}

.message {
  margin-bottom: 16px;
}

.userMessage {
  background-color: var(--mantine-color-blue-5);
  color: white;
  padding: 12px;
  border-radius: 24px;
}
