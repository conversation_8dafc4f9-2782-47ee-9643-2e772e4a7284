import { Box, Textarea, Flex, Select, Button, Group, Text } from '@mantine/core';
import { IconPlayerStopFilled, IconCircleArrowUpFilled } from '@tabler/icons-react';
import classes from './SearchBox.module.scss';
import { useEffect, useState } from 'react';
import { Trip } from '@shared/types/trip';

export interface SearchBoxProps {
  trips: Trip[];
  searching: boolean;
  onSearch: (query: string, tripId: string | null) => void;
}

const ALL_TRIPS = {
  value: '',
  label: 'All Trips',
};

export const SearchBox = ({ trips, searching, onSearch }: SearchBoxProps) => {
  const [query, setQuery] = useState('');
  const [tripOptions, setTripOptions] = useState<{ value: string; label: string }[]>();
  const [selectedTrip, setSelectedTrip] = useState<string | null>(null);
  const [showAlert, setShowAlert] = useState(true);

  useEffect(() => {
    if (trips?.length) {
      setTripOptions([
        ...trips.map((trip) => ({
          value: trip.id,
          label: trip.name,
        })),
        ALL_TRIPS,
      ]);
      setSelectedTrip(trips[0].id);
    } else {
      setTripOptions([ALL_TRIPS]);
      setSelectedTrip(ALL_TRIPS.value);
    }
  }, [trips]);

  const handleSearch = () => {
    if (query) {
      onSearch(query, selectedTrip === ALL_TRIPS.value ? null : selectedTrip);
    }
    setQuery('');
    setShowAlert(false);
  };

  return (
    <>
      <Box className={classes.box}>
        <Textarea
          variant="unstyled"
          placeholder="How can Tripppy help you today?"
          rows={3}
          value={query}
          onChange={(event) => setQuery(event.target.value)}
          onKeyDown={(event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
              event.preventDefault();
              handleSearch();
            }
          }}
        />

        <Flex justify="space-between" align="center">
          <Select
            // variant="unstyled"
            c="gray.2"
            radius="xl"
            w="fit-content"
            data={tripOptions}
            value={selectedTrip}
            onChange={setSelectedTrip}
            styles={(theme) => ({
              input: {
                backgroundColor: theme.colors.gray[1],
                border: 'transparent',
              },
            })}
            // rightSection={<IconChevronDown />}
          />
          <Button
            variant="subtle" // Optional: Use 'subtle' to remove background and borders
            p={0} // Remove padding
            styles={{
              root: {
                backgroundColor: 'transparent', // Remove background
                border: 'none', // Remove borders
                width: 'auto', // Let the button size adjust to the icon
                height: 'auto',
              },
            }}
            disabled={!query || !selectedTrip}
            onClick={handleSearch}
          >
            {searching ? (
              <IconPlayerStopFilled stroke={2} size={32} color="var(--mantine-color-blue-5)" />
            ) : (
              <IconCircleArrowUpFilled
                stroke={2}
                size={32}
                color={query ? 'var(--mantine-color-blue-5)' : 'var(--mantine-color-gray-5)'}
              />
            )}
          </Button>
        </Flex>
      </Box>

      <Group mt="lg" gap={10}>
        <Button
          variant="outline"
          color="gray"
          size="xs"
          radius="xl"
          onClick={() => setQuery('Does my hotel have free WiFi?')}
        >
          Does my hotel have free WiFi?
        </Button>
        <Button
          variant="outline"
          color="gray"
          size="xs"
          radius="xl"
          onClick={() => setQuery('What’s my layover duration?')}
        >
          What’s my layover duration?
        </Button>
        <Button
          variant="outline"
          color="gray"
          size="xs"
          radius="xl"
          onClick={() => setQuery('Is breakfast included in my stay?')}
        >
          Is breakfast included in my stay?
        </Button>
        <Button variant="outline" color="gray" size="xs" radius="xl" onClick={() => setQuery('Can I check in online?')}>
          Can I check in online?
        </Button>
      </Group>
      {showAlert && (
        <Text ta={'center'} c="dimmed" mt="xl" fz={'sm'}>
          Tripppy can make mistakes, so double-check its responses.
        </Text>
      )}
    </>
  );
};
