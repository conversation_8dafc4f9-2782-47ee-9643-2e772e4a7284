import { Box, Stack, Text, Group, Divider, LoadingOverlay } from '@mantine/core';
import { useEffect, useState } from 'react';
import { askAgent } from '../../services/agentService';
import ReactMarkdown from 'react-markdown';
import { SearchBox } from './SearchBox';
import { useTextStreaming } from '../../hooks/useTextStreaming';
import classes from './AgentPage.module.scss';
import { Trip } from '@shared/types/trip';
import { getTrips } from '../../services/tripService';
import { NoTrips } from '../../components/NoTrips';
import { SearchItineraryLoader } from './SearchItineraryLoader';
import { AgentContext } from '@shared/types/agent';
import { downloadDocument } from '../../services/documentService';
import { LanguageModelV1Source } from '@ai-sdk/provider';
import { getSourceDomain } from './utils';
import { SourceBadge } from './SourceBadge';

interface AgentMessage {
  message: string;
  role: 'user' | 'assistant';
  contexts?: AgentContext[];
  sources?: LanguageModelV1Source[];
}

export const AgentPage = () => {
  const [searching, setSearching] = useState(false);
  const [searched, setSearched] = useState(false);
  const [trips, setTrips] = useState<Trip[]>();
  const [loading, setLoading] = useState(false);
  const { streamedText, streamText, setStreamedText } = useTextStreaming();

  const [messages, setMessages] = useState<AgentMessage[]>([]);

  useEffect(() => {
    const fetchTrips = async () => {
      setLoading(true);
      const trips = await getTrips();
      setTrips(trips);
      setLoading(false);
    };
    fetchTrips();
  }, []);

  const onSearch = async (prompt: string, tripId: string | null) => {
    setSearched(true);
    setSearching(true);
    setStreamedText(''); // Clear previous text

    const request: AgentMessage[] = [...messages, { message: prompt, role: 'user' }];

    setMessages(request);

    try {
      const response = await askAgent(prompt, tripId);
      setSearching(false);
      await streamText(response.content);
      setStreamedText('');
      setMessages((prev) => [
        ...prev,
        {
          message: response.content,
          role: 'assistant',
          sources: response.sources,
          contexts: response.contexts,
        },
      ]);
    } catch (error) {
      setSearching(false);
      console.error('Error:', error);
    }
  };

  const handleViewDocument = async (tripId: string, documentId: string) => {
    const blob = await downloadDocument(tripId, documentId);
    const url = window.URL.createObjectURL(blob);
    window.open(url, '_blank');
  };

  if (trips?.length === 0) {
    return <NoTrips trips={trips} />;
  }

  return (
    <>
      <LoadingOverlay visible={loading} style={{ backdropFilter: 'blur(2px)' }} />

      {trips?.length === 0 && <NoTrips trips={trips} />}

      {(!trips || trips.length > 0) && (
        <Stack className={classes.container} gap="md">
          {searched && (
            <div className={classes.topSection}>
              {messages.map((message, index) =>
                message.role === 'user' ? (
                  <Box key={index} ta="right" className={classes.message} mt="xl">
                    <span className={classes.userMessage}>{message.message}</span>
                  </Box>
                ) : (
                  <Box key={index} ta="left" className={classes.message} mt="xl" mb="64px">
                    <ReactMarkdown>{message.message}</ReactMarkdown>
                    {(message.contexts?.length || message.sources?.length) && (
                      <>
                        <Divider />

                        <Box mt="sm">
                          <Text size="sm" fw={500} c="dimmed">
                            Sources:
                          </Text>
                          <Group gap={4} mt={4}>
                            {message.contexts && message.contexts.length > 0
                              ? message.contexts?.map((context, index) => (
                                  <SourceBadge
                                    key={index}
                                    onClick={() => handleViewDocument(context.tripId, context.documentId)}
                                  >
                                    {context.documentName}
                                  </SourceBadge>
                                ))
                              : message.sources?.map((source, index) => (
                                  <SourceBadge key={index} onClick={() => window.open(source.url, '_blank')}>
                                    {getSourceDomain(source.url)}
                                  </SourceBadge>
                                ))}
                          </Group>
                        </Box>
                      </>
                    )}
                  </Box>
                ),
              )}

              {searching && <SearchItineraryLoader />}

              {streamedText && (
                <Box mt="xl">
                  <ReactMarkdown>{streamedText}</ReactMarkdown>
                </Box>
              )}
            </div>
          )}
          <div className={classes.bottomSection}>
            <SearchBox trips={trips!} searching={searching} onSearch={onSearch} />
          </div>
        </Stack>
      )}
    </>
  );
};
