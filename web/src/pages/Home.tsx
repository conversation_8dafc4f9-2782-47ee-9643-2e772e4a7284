import { SimpleGrid, Title, Text, Image, Flex, Button } from '@mantine/core';
import classes from './Home.module.scss';
import luggage from '../assets/luggage.jpg';
import { useNavigate } from 'react-router-dom';

export const Home = () => {
  const navigate = useNavigate();

  return (
    <>
      <SimpleGrid cols={2}>
        <div>
          <Title order={1} size={48}>
            All your <span className={classes.underlined}>travel details</span>,{' '}
            <span className={classes.underlined}>one question</span> away.
          </Title>

          <Text mt={40}>
            Say goodbye to sifting through emails — our AI organizes your plans and answers your questions instantly.
          </Text>

          <Flex justify="center" mt={80}>
            <Button size="xl" onClick={() => navigate('/trips/edit')}>
              Plan a Trip
            </Button>
          </Flex>
        </div>
        <Flex justify="flex-end">
          <Image src={luggage} radius={8} h={500} w="auto" />
        </Flex>
      </SimpleGrid>
      <Text mt={40} size="lg" ta="justify">
        Simply upload your travel documents — whether it’s hotel, flight, or rental reservations — and let our
        intelligent assistant do the rest. It instantly organizes your trip details and provides quick answers to all
        your questions, like check-in times, flight connections, and more. With everything in one place, your trip
        planning becomes effortless and stress-free, so you can focus on enjoying your travels.
      </Text>
    </>
  );
};
