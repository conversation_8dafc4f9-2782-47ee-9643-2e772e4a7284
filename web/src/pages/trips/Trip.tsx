import { ActionIcon, Card, Flex, Group, HoverCard, Image, List, Menu, Stack, Text } from '@mantine/core';
import classes from './Trip.module.scss';
import { IconCalendar, IconDotsVertical, IconEdit, IconPaperclip, IconTrash, IconUser } from '@tabler/icons-react';
import { forwardRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { modals } from '@mantine/modals';
import { Trip } from '@shared/types/trip';

interface TripProps {
  trip: Trip;
  onDelete: (tripId: string) => void;
}

export const TripComponent = ({ trip, onDelete }: TripProps) => {
  const navigate = useNavigate();

  const images = [
    'https://images.unsplash.com/photo-1529154036614-a60975f5c760?q=80&w=2076&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    'https://plus.unsplash.com/premium_photo-1661919210043-fd847a58522d?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    'https://images.unsplash.com/photo-1544918877-460635b6d13e?q=80&w=2076&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    'https://images.unsplash.com/photo-1533929736458-ca588d08c8be?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    'https://images.unsplash.com/photo-1485871981521-5b1fd3805eee?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  ];
  // const cities = ['Rome Summer Trip', 'Paris', 'Barcelona', 'London', 'New York'];

  const Item = forwardRef<
    HTMLDivElement,
    {
      Icon: React.ElementType;
      children?: React.ReactNode;
    }
  >((props, ref) => {
    const { Icon, children, ...others } = props;
    return (
      <Flex gap={8} ref={ref} {...others} w="fit-content">
        <Icon stroke={2} size={18} style={{ color: 'var(--mantine-color-gray-5' }} />
        {children}
      </Flex>
    );
  });

  const openDeleteConfirmation = () =>
    modals.openConfirmModal({
      title: 'Delete trip',
      centered: true,
      children: <Text size="sm">Are you sure you want to delete this trip? This action cannot be undone.</Text>,
      labels: { confirm: 'Delete', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onCancel: () => console.log('Cancelled'),
      onConfirm: () => onDelete(trip.id),
    });

  return (
    <Card withBorder radius="md" p={0} className={classes.card}>
      <Group gap={0} align="top">
        <Image src={images[Number(trip.id)]} height={140} />
        <Stack p="md" gap={4} flex={1}>
          {/* <Anchor underline="hover" size="xl" fw={500}>
            {cities[id]}
          </Anchor> */}
          <Text size="xl" fw={500}>
            {trip.name}
          </Text>
          {trip.description && (
            <Text size="sm" c="dimmed" mb="sm">
              {trip.description}
            </Text>
          )}
          <Item Icon={IconCalendar}>
            <Text size="sm">
              {trip.dateFrom} - {trip.dateTo} (3 days)
            </Text>
          </Item>
          <Item Icon={IconUser}>
            <Text size="sm">Shared with: {trip.companions?.join(', ')}</Text>
          </Item>
          <HoverCard width={280} shadow="md">
            <HoverCard.Target>
              <Item Icon={IconPaperclip}>
                <Text size="sm">{trip.documents?.length || 0} documents uploaded</Text>
              </Item>
            </HoverCard.Target>
            {trip.documents?.length ? (
              <HoverCard.Dropdown>
                <List size="sm">
                  {trip.documents.map((file, index) => (
                    <List.Item key={index}>{file.name}</List.Item>
                  ))}
                </List>
              </HoverCard.Dropdown>
            ) : null}
          </HoverCard>
        </Stack>
        <Stack p="md" gap={4} align="end" flex={0}>
          <Menu position="bottom-end">
            <Menu.Target>
              <ActionIcon variant="subtle" size="md">
                <IconDotsVertical size="1rem" />
              </ActionIcon>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item leftSection={<IconEdit size="1rem" />} onClick={() => navigate(`/trips/edit?id=${trip.id}`)}>
                Edit
              </Menu.Item>
              <Menu.Item color="red" leftSection={<IconTrash size="1rem" />} onClick={openDeleteConfirmation}>
                Delete
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Stack>
      </Group>
    </Card>
  );
};
