import { Button, Group, SegmentedControl, Loader, Center } from '@mantine/core';
import { TripComponent } from './Trip';
import { IconPlus } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { useEffect, useState } from 'react';
import { getTrips } from '../../services/tripService';
import { Trip } from '@shared/types/trip';
import { AxiosError } from 'axios';
import { NewTripModal } from './new-trip/NewTripModal';
import { deleteTrip } from '../../services/tripService';
import { showErrorNotification, showSuccessNotification } from './utils';
import { NoTrips } from '../../components/NoTrips';

export const Trips = () => {
  const [opened, { open, close }] = useDisclosure(false);

  const [trips, setTrips] = useState<Trip[]>();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [loading, setLoading] = useState<boolean>(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [, setError] = useState<string | null>(null);

  const fetchTrips = async () => {
    setLoading(true);

    try {
      const data = await getTrips();
      setTrips(data);
    } catch (err) {
      if (err instanceof AxiosError) {
        // Now TypeScript knows err is an AxiosError
        setError('Failed to load trips');
      } else {
        // If it's not an AxiosError, handle it here (optional)
        console.error('An unexpected error occurred:', err);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrips();
  }, []);

  const handleDelete = async (tripId: string) => {
    try {
      setTrips(undefined);
      setLoading(true);
      await deleteTrip(tripId);
      showSuccessNotification('deleted');
      await fetchTrips();
    } catch (e) {
      showErrorNotification('deleting');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <NewTripModal opened={opened} close={close} />

      <Group w="100%" mb="20px">
        <SegmentedControl flex="1" data={['Current / Upcoming', 'Past']} />
        <Button onClick={open}>
          <IconPlus stroke={2} size={18} /> Add Trip
        </Button>
      </Group>

      {loading && (
        <Center>
          <Loader color="blue" type="bars" />
        </Center>
      )}

      {trips?.map((trip) => <TripComponent key={trip.id} trip={trip} onDelete={handleDelete} />)}

      <NoTrips trips={trips} />
    </>
  );
};
