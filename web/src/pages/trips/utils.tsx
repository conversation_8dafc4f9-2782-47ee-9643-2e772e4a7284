import { IconCheck, IconX } from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';

export const showSuccessNotification = (operation: 'saved' | 'edited' | 'deleted') => {
  notifications.show({
    title: 'Success!',
    message: `Your trip has been ${operation} successfully.`,
    color: 'green',
    icon: <IconCheck size="1.2rem" />,
    autoClose: 3000,
    withCloseButton: true,
  });
};

export const showErrorNotification = (operation: 'loading' | 'saving' | 'editing' | 'deleting') => {
  notifications.show({
    title: 'Oops!',
    message: `There has been a problem with ${operation} your trip`,
    color: 'red',
    icon: <IconX size="1.2rem" />,
    autoClose: 3000,
    withCloseButton: true,
  });
};

export const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) {
    return `${bytes} bytes`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(2)} KB`;
  } else {
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  }
};
