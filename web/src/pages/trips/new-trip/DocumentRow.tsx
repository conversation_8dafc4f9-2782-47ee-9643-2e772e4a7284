import { useEffect, useState, useRef } from 'react';
import { downloadDocument, uploadDocument } from '../../../services/documentService';
import { <PERSON>chor, Button, Group, RingProgress, Table, Text, Tooltip } from '@mantine/core';
import { Document } from './document';
import { IconDownload, IconFileTypePdf, IconFileTypeTxt, IconTrash } from '@tabler/icons-react';
import { formatFileSize } from '../utils';
import classes from './DocumentRow.module.scss';

interface DocumentRowProps {
  document: Document;
  tripId: string;
  onRemove: (document: Document) => void;
}

export const DocumentRow = ({ document, tripId, onRemove }: DocumentRowProps) => {
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadError, setUploadError] = useState<string>();
  const [uploading, setUploading] = useState<boolean>(false);
  const hasStartedUpload = useRef(false);

  useEffect(() => {
    if (document.file) {
      // Newly uploaded file
      if (hasStartedUpload.current) return;
      hasStartedUpload.current = true;

      async function handleUpload() {
        try {
          setUploading(true);
          await uploadDocument({
            tripId,
            file: document.file!,
            documentId: document.id,
            contentType: document.contentType,
            onProgress: setUploadProgress,
          });
          setUploading(false);
        } catch (error) {
          setUploadError('Upload failed');
          setUploading(false);
        }
      }

      handleUpload();
    }
  }, []);

  const handleDownload = async (e: React.MouseEvent, inline: boolean) => {
    e.preventDefault();
    const blob = await downloadDocument(tripId, document.id);
    const url = window.URL.createObjectURL(blob);

    if (inline) {
      window.open(url, '_blank');
    } else {
      const a = window.document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = document.name || '';
      window.document.body.appendChild(a);
      a.click();
      window.document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }
  };

  const getDimmedColor = () => {
    return uploading ? 'var(--mantine-color-dimmed)' : undefined;
  };

  return (
    <Table.Tr>
      <Table.Td className={classes.tableCell}>
        <Group gap={4}>
          {document.contentType === 'application/pdf' && (
            <IconFileTypePdf stroke={1} size={20} color={getDimmedColor()} />
          )}
          {document.contentType === 'text/plain' && <IconFileTypeTxt stroke={1} size={20} color={getDimmedColor()} />}
          <Anchor size="xs" onClick={(e) => handleDownload(e, true)} c={getDimmedColor()}>
            {document.name}
          </Anchor>
        </Group>
      </Table.Td>
      <Table.Td className={classes.tableCell} align="right">
        <Text size="xs" c={getDimmedColor()}>
          {formatFileSize(document.size || 0)}
        </Text>
      </Table.Td>
      <Table.Td className={classes.tableCell} align="right" valign="middle" w={150}>
        {uploading ? (
          <RingProgress size={16} thickness={1} sections={[{ value: uploadProgress || 0, color: 'blue' }]} />
        ) : uploadError ? (
          <Text c="red" size="xs">
            {uploadError}
          </Text>
        ) : (
          <Group gap={8} justify="flex-end">
            <Tooltip label={`Download ${document.name}`}>
              <Button
                variant="subtle" // Optional: Use 'subtle' to remove background and borders
                p={0} // Remove padding
                styles={{
                  root: {
                    backgroundColor: 'transparent', // Remove background
                    border: 'none', // Remove borders
                    width: 'auto', // Let the button size adjust to the icon
                    height: 'auto',
                  },
                }}
                onClick={(e) => handleDownload(e, false)}
                disabled={uploading}
              >
                <IconDownload stroke={2} size={18} />
              </Button>
            </Tooltip>
            <Tooltip label={`Delete ${document.name}`}>
              <Button
                variant="subtle" // Optional: Use 'subtle' to remove background and borders
                p={0} // Remove padding
                styles={{
                  root: {
                    backgroundColor: 'transparent', // Remove background
                    border: 'none', // Remove borders
                    width: 'auto', // Let the button size adjust to the icon
                    height: 'auto',
                  },
                }}
                onClick={() => onRemove(document)}
                disabled={uploading}
              >
                <IconTrash stroke={2} size={18} />
              </Button>
            </Tooltip>
          </Group>
        )}
      </Table.Td>
    </Table.Tr>
  );
};
