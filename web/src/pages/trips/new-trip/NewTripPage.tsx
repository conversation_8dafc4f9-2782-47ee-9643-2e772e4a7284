import { useNavigate } from 'react-router-dom';
import { NewTrip as NewTripPanel } from './NewTrip';
import { Center, Container } from '@mantine/core';
import { saveTrip } from '../../../services/tripService';
import { Trip } from '@shared/types/trip';
import { showErrorNotification, showSuccessNotification } from '../utils';

export const NewTripPage = () => {
  const navigate = useNavigate();

  const handleSave = async (trip: Trip, isEdit: boolean) => {
    try {
      await saveTrip(trip, isEdit);
      showSuccessNotification(isEdit ? 'edited' : 'saved');
      navigate('/trips');
    } catch (error) {
      showErrorNotification(isEdit ? 'editing' : 'saving');
    }
  };

  return (
    <Center>
      <Container size="sm">
        <NewTripPanel onSave={handleSave} onCancel={() => navigate('/trips')} />
      </Container>
    </Center>
  );
};
