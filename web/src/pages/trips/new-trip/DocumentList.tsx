import { Group, Button, Table, FileButton, Text } from '@mantine/core';
import { IconTrash, IconPlus } from '@tabler/icons-react';
import { DocumentRow } from './DocumentRow';
import { useState } from 'react';
import { Document } from './document';

export interface DocumentListProps {
  documents?: Document[];
  onChange: (documents: Document[]) => void;
  tripId: string;
}

const MAX_UPLOAD_FILES = 10;
const FILE_MAX_SIZE = 5;

export const DocumentList = ({ documents, onChange, tripId }: DocumentListProps) => {
  const [fileKey, setFileKey] = useState(0);

  const handleUpload = async (files: File[]) => {
    onChange([
      ...(documents || []),
      ...files.map((file) => ({
        id: crypto.randomUUID(),
        name: file.name,
        size: file.size,
        contentType: file.type,
        file,
      })),
    ]);
  };

  const handleRemoval = async (documentsToRemove: Document[]) => {
    const remainingDocuments = documents!.filter(
      (document) => !documentsToRemove.some((doc) => doc.id === document.id),
    );
    onChange(remainingDocuments);
    setFileKey((prev) => prev + 1);
  };

  return (
    <>
      {documents && documents.length > 0 ? (
        <>
          <Group gap="xs">
            <Text size="sm" style={{ fontWeight: 500 }}>
              Documents
            </Text>

            <Button
              variant="subtle" // Optional: Use 'subtle' to remove background and borders
              p={0} // Remove padding
              styles={{
                root: {
                  backgroundColor: 'transparent', // Remove background
                  border: 'none', // Remove borders
                  width: 'auto', // Let the button size adjust to the icon
                  height: 'auto',
                },
              }}
              onClick={() => handleRemoval(documents)}
            >
              <IconTrash stroke={2} size={18} />
            </Button>
          </Group>
          <Table mt={-10}>
            <Table.Tbody>
              {documents.map((document) => (
                <DocumentRow
                  key={document.id || document.file?.name}
                  document={document}
                  tripId={tripId!}
                  onRemove={(document) => handleRemoval([document])}
                />
              ))}
            </Table.Tbody>
          </Table>
        </>
      ) : null}

      <Group justify="flex-start">
        <FileButton onChange={handleUpload} accept="application/pdf,text/plain" multiple key={fileKey}>
          {(props) => (
            <>
              <Button
                variant="transparent"
                {...props}
                m={0}
                p={0}
                disabled={!tripId || (documents && documents.length >= MAX_UPLOAD_FILES)}
                styles={{
                  root: {
                    backgroundColor: 'transparent',
                    '&:disabled': {
                      backgroundColor: 'transparent !important',
                      border: 'none',
                      backgroundImage: 'none',
                    },
                  },
                }}
              >
                <IconPlus stroke={2} size={18} /> Add Documents
              </Button>
              <Text size="sm" c={'dimmed'}>
                (Max {MAX_UPLOAD_FILES} files, {FILE_MAX_SIZE}MB each)
              </Text>
            </>
          )}
        </FileButton>
        <Text size="xs" ta="justify">
          While your hotel, flight, and other reservations are processed when you forward emails to us, you can upload
          extra documents here. Add itineraries, packing lists, or any other important notes in TXT, PDF, or other
          human-readable formats. Once uploaded, you’ll be able to search and ask questions about them.
        </Text>
      </Group>
    </>
  );
};
