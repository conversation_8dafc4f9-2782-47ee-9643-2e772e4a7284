import { Stack, TextInput, TagsInput, Textarea, Group, Button, LoadingOverlay } from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { isEmail, isNotEmpty, useForm } from '@mantine/form';
import { getTripById } from '../../../services/tripService';
import { Trip } from '@shared/types/trip';
import { DocumentList } from './DocumentList';
import { Document } from './document';
import { getISODate } from '@shared/utils';
import { showErrorNotification } from '../utils';

interface NewTripProps {
  onSave: (trip: Trip, isEdit: boolean) => Promise<void>;
  onCancel: () => void;
}

export const NewTrip = ({ onSave, onCancel }: NewTripProps) => {
  const [documents, setDocuments] = useState<Document[]>();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const [editMode] = useState<boolean>(queryParams.has('id'));
  const [tripId] = useState<string>(queryParams.get('id') ?? crypto.randomUUID());

  const [loading, setLoading] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);

  const form = useForm({
    mode: 'controlled',
    initialValues: {
      name: '',
      dates: [null, null] as [Date | null, Date | null],
      description: '',
      companions: [] as string[],
    },
    validate: {
      name: isNotEmpty(),
      dates: ([start, end]) => (start && end ? null : true),
      companions: (companions) =>
        companions?.every((email) => isEmail()(email) === null) ? null : 'All emails must be valid',
    },
    validateInputOnChange: ['companions'],
  });

  useEffect(() => {
    if (editMode) {
      const fetchTrip = async () => {
        setLoading(true);
        try {
          const data = await getTripById(tripId);
          form.setValues({
            name: data.name,
            dates: [data.dateFrom ? new Date(data.dateFrom) : null, data.dateTo ? new Date(data.dateTo) : null],
            description: data.description,
            companions: data.companions,
          });
          setDocuments(data.documents);
        } catch (err) {
          showErrorNotification('loading');
        } finally {
          setLoading(false);
        }
      };

      fetchTrip();
    }
  }, []);

  const handleSave = async () => {
    setSaving(true);

    const trip: Trip = {
      id: tripId,
      name: form.values.name,
      dateFrom: getISODate(form.values.dates[0]),
      dateTo: getISODate(form.values.dates[1]),
      description: form.values.description,
      companions: form.values.companions,
      documents: documents?.map(({ file, ...documentMetadata }) => documentMetadata),
    };

    await onSave(trip, editMode);
    setSaving(false);
  };

  const handleCancel = () => {
    setDocuments([]);
    onCancel();
  };

  return (
    <>
      <LoadingOverlay visible={loading} style={{ backdropFilter: 'blur(2px)' }} />

      <form onSubmit={form.onSubmit(handleSave)} onReset={handleCancel}>
        <Stack p={10}>
          <TextInput
            label="Trip Name"
            placeholder="E.g. London summer trip 2025"
            withAsterisk
            key={form.key('name')}
            {...form.getInputProps('name')}
          />
          <DatePickerInput
            type="range"
            label="Dates"
            minDate={new Date()}
            placeholder="Enter the dates of your trip..."
            withAsterisk
            clearable
            key={form.key('dates')}
            {...form.getInputProps('dates')}
          />
          <TagsInput
            label="Share with your companions"
            placeholder="Enter the email addresses of your companions..."
            clearable
            splitChars={[' ', ',', ';']} // Allows emails to be added on space, comma, or semicolon
            key={form.key('companions')}
            {...form.getInputProps('companions')}
          />
          <Textarea
            label="Description"
            placeholder="Enter a description for your trip..."
            minRows={3}
            autosize
            key={form.key('description')}
            {...form.getInputProps('description')}
            maxLength={256}
          />

          <DocumentList documents={documents} onChange={setDocuments} tripId={tripId} />

          <Group justify="flex-end">
            <Button type="reset" variant="subtle">
              Cancel
            </Button>
            <Button type="submit" disabled={!form.isValid()} loading={saving}>
              Save
            </Button>
          </Group>
        </Stack>
      </form>
    </>
  );
};
