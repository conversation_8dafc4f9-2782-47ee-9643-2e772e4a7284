import { Button, Group, SimpleGrid, Textarea, TextInput } from '@mantine/core';
import { useForm } from '@mantine/form';

export const Contact = () => {
  const form = useForm({
    initialValues: {
      name: '',
      email: '',
      subject: '',
      message: '',
    },
    validate: {
      name: (value) => value.trim().length < 2,
      email: (value) => !/^\S+@\S+$/.test(value),
      subject: (value) => value.trim().length === 0,
      message: (value) => value.trim().length === 0,
    },
  });

  return (
    <form onSubmit={form.onSubmit(() => {})}>
      <SimpleGrid cols={{ base: 1, sm: 2 }}>
        <TextInput label="Name" placeholder="Your name" name="name" {...form.getInputProps('name')} />
        <TextInput label="Email" placeholder="Your email" name="email" {...form.getInputProps('email')} />
      </SimpleGrid>

      <TextInput label="Subject" placeholder="Subject" mt="md" name="subject" {...form.getInputProps('subject')} />
      <Textarea
        mt="md"
        label="Message"
        placeholder="Your message"
        maxRows={10}
        minRows={5}
        autosize
        name="message"
        {...form.getInputProps('message')}
      />

      <Group justify="center" mt="xl">
        <Button type="submit">Send message</Button>
      </Group>
    </form>
  );
};
