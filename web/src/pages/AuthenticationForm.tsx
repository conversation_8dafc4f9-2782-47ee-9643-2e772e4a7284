import { <PERSON>chor, Button, Center, Divider, Group, Paper, PasswordInput, Stack, Text, TextInput } from '@mantine/core';
import { useForm } from '@mantine/form';
import { upperFirst } from '@mantine/hooks';
import { GoogleButton } from '../components/GoogleButton.tsx';
import { useState } from 'react';
import { signIn, signInWithRedirect, signUp } from '@aws-amplify/auth';
import { useNavigate } from 'react-router-dom';

interface AuthenticationFormProps {
  type: 'login' | 'register';
}

export function AuthenticationForm(props: AuthenticationFormProps) {
  const [type, setType] = useState(props.type ?? 'register');
  const navigate = useNavigate();

  const form = useForm({
    initialValues: {
      email: '',
      name: '',
      password: '',
    },

    validate: {
      email: (val) => (/^\S+@\S+$/.test(val) ? null : 'Invalid email'),
      password: (val) => (val.length <= 6 ? 'Password should include at least 6 characters' : null),
    },
  });

  // Google Sign-In Handler
  const handleGoogleSignIn = async () => {
    try {
      // Initiate the Google sign-in flow
      await signInWithRedirect({ provider: 'Google' });
    } catch (error) {
      console.error('Error during Google sign-in', error);
    }
  };

  async function registerUser() {
    try {
      const result = await signUp({
        username: form.values.email,
        password: form.values.password,
        options: {
          userAttributes: {
            email: form.values.email,
            given_name: form.values.name,
          },
        },
      });

      await signIn({ username: form.values.email, password: form.values.password });

      console.log('Sign-up successful:', result);
    } catch (error) {
      console.error('Error during sign-up:', error);
    }
  }

  const handleSubmit = async () => {
    if (type === 'login') {
      try {
        await signIn({ username: form.values.email, password: form.values.password });
        navigate('/');
      } catch (error) {
        console.error('Error during sign-in:', error);
      }
    } else if (type === 'register') {
      await registerUser();
    }
  };

  return (
    <Center>
      <Paper radius="md" p="xl" withBorder w={600}>
        <Text size="lg" fw={500}>
          Welcome to Tripppy, {type} with
        </Text>

        <Group grow mb="md" mt="md">
          <GoogleButton radius="xl" onClick={handleGoogleSignIn}>
            Google
          </GoogleButton>
          {/* <TwitterButton radius="xl">Twitter</TwitterButton> */}
        </Group>

        <Divider label="Or continue with email" labelPosition="center" my="lg" />

        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack>
            {type === 'register' && (
              <TextInput
                label="Name"
                placeholder="Your name"
                value={form.values.name}
                onChange={(event) => form.setFieldValue('name', event.currentTarget.value)}
                radius="md"
              />
            )}

            <TextInput
              required
              label="Email"
              placeholder="<EMAIL>"
              value={form.values.email}
              onChange={(event) => form.setFieldValue('email', event.currentTarget.value)}
              error={form.errors.email && 'Invalid email'}
              radius="md"
            />

            <PasswordInput
              required
              label="Password"
              placeholder="Your password"
              value={form.values.password}
              onChange={(event) => form.setFieldValue('password', event.currentTarget.value)}
              error={form.errors.password && 'Password should include at least 6 characters'}
              radius="md"
            />
          </Stack>

          <Group justify="space-between" mt="xl">
            <Anchor
              component="button"
              type="button"
              c="dimmed"
              onClick={() => setType(type === 'login' ? 'register' : 'login')}
              size="xs"
            >
              {type === 'register' ? 'Already have an account? Login' : "Don't have an account? Sign up"}
            </Anchor>
            <Button type="submit" radius="xl">
              {upperFirst(type)}
            </Button>
          </Group>
        </form>
      </Paper>
    </Center>
  );
}
