import { ResourcesConfig } from 'aws-amplify';

export const awsConfig: ResourcesConfig = {
  Auth: {
    Cognito: {
      userPoolId: 'us-east-1_CJwE2wg1s',
      userPoolClientId: '29a99ra8a0v8jaelrv<PERSON>i30eff',
      loginWith: {
        oauth: {
          domain: 'us-east-1cjwe2wg1s.auth.us-east-1.amazoncognito.com',
          scopes: ['openid', 'email', 'profile'],
          redirectSignIn: ['http://localhost:5173', 'https://d84l1y8p4kdic.cloudfront.net', 'https://www.tripppy.co'],
          redirectSignOut: ['http://localhost:5173', 'https://www.tripppy.co'],
          responseType: 'code',
          providers: ['Google'],
        },
      },
    },
  },
};
