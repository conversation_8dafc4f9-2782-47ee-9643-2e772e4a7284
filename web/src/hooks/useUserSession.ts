import { fetchAuthSession } from '@aws-amplify/auth';
import { useEffect, useState } from 'react';

export const useUserSession = () => {
  const [firstName, setFirstName] = useState<string>();
  const [lastName, setLastName] = useState<string>();

  useEffect(() => {
    const checkUserSession = async () => {
      try {
        const session = await fetchAuthSession();

        if (session.tokens) {
          const { given_name: firstName, family_name: lastName } =
            (session.tokens?.idToken?.payload as { given_name?: string; family_name?: string }) || {};
          setFirstName(firstName);
          setLastName(lastName);
        }
      } catch (error) {
        setFirstName(undefined);
        setLastName(undefined);
      }
    };

    checkUserSession();
  }, []);

  return { firstName, lastName };
};
