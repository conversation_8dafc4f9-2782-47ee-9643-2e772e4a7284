import { AuthUser, getCurrentUser } from '@aws-amplify/auth';
import { Hub } from '@aws-amplify/core';
import { useEffect, useState, useCallback } from 'react';

interface AuthState {
  authenticated: boolean | null;
  user: AuthUser | null;
}

export const useAuth = (): AuthState => {
  const [state, setState] = useState<AuthState>({
    authenticated: null,
    user: null,
  });

  const checkAuth = useCallback(async () => {
    try {
      const user = await getCurrentUser();
      setState({ authenticated: true, user });
    } catch {
      setState({ authenticated: false, user: null });
    }
  }, []);

  useEffect(() => {
    checkAuth();

    const unsubscribe = Hub.listen('auth', ({ payload }) => {
      if (payload.event === 'signedIn' || payload.event === 'signedOut') {
        checkAuth();
      }
    });

    return () => unsubscribe();
  }, [checkAuth]);

  return state;
};
