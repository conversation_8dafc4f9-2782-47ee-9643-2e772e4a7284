import { useState, useCallback, useRef, useEffect } from 'react';

export const useTextStreaming = () => {
  const [streamedText, setStreamedText] = useState('');
  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const streamText = useCallback(async (text: string) => {
    // Clean up any existing stream
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this stream
    abortControllerRef.current = new AbortController();
    const { signal } = abortControllerRef.current;

    const words = text.split(' ');
    let currentText = '';

    try {
      for (const word of words) {
        // Check if streaming should stop
        if (signal.aborted) {
          return;
        }

        await new Promise((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            if (!signal.aborted) {
              currentText += (currentText ? ' ' : '') + word;
              setStreamedText(currentText);
              resolve(undefined);
            } else {
              reject(new Error('Streaming aborted'));
            }
          }, 10);

          // Cleanup timeout if aborted
          signal.addEventListener('abort', () => {
            clearTimeout(timeoutId);
            reject(new Error('Streaming aborted'));
          });
        });
      }
    } catch (error: any) {
      if (error?.message === 'Streaming aborted') {
        console.log('Streaming was aborted');
      } else {
        console.error('Streaming error:', error);
      }
    }
  }, []);

  return {
    streamedText,
    streamText,
    setStreamedText,
  };
};
