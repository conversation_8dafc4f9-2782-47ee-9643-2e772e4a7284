import React, { ReactNode } from 'react';
import { AppShell, Container } from '@mantine/core';

interface HolyGrailLayoutProps {
  header: ReactNode;
  main: ReactNode;
  footer: ReactNode;
}

const HolyGrailLayout: React.FC<HolyGrailLayoutProps> = ({ header, main, footer }) => {
  return (
    <AppShell
      styles={{
        header: { borderBottom: 'none' },
        footer: { borderTop: 'none', position: 'relative', bottom: 0 },
        main: { minHeight: 'calc(100vh - 140px)' },
      }}
    >
      <AppShell.Header h={80}>
        <Container size="lg" h="100%">
          {header}
        </Container>
      </AppShell.Header>

      <AppShell.Main pt={120}>
        <Container size="lg">{main}</Container>
      </AppShell.Main>

      <AppShell.Footer h={30} mt={80}>
        <Container size="lg" h="100%">
          {footer}
        </Container>
      </AppShell.Footer>
    </AppShell>
  );
};

export default HolyGrailLayout;
