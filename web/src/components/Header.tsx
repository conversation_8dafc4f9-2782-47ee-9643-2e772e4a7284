import { <PERSON>, Button, Group, Menu, Text, UnstyledButton } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { NavLink, useNavigate } from 'react-router-dom';
import classes from './Header.module.scss';
import { signOut } from '@aws-amplify/auth';
import { useAuth } from '../hooks/useAuth';
import cx from 'clsx';
import { IconChevronDown, IconSettings, IconLogout, IconUserCircle } from '@tabler/icons-react';
import { useState } from 'react';
import AuthGuard from './AuthGuard';
import { useUserSession } from '../hooks/useUserSession';

export function Header() {
  const navigate = useNavigate();
  const { authenticated } = useAuth();
  const { firstName } = useUserSession();
  const [userMenuOpened, setUserMenuOpened] = useState(false);
  const [opened, { toggle }] = useDisclosure(false);

  const getClassName = ({ isActive }: { isActive: boolean }) => `${classes.link} ${isActive ? classes.activeLink : ''}`;

  <NavLink to="/" className={getClassName}>
    Home
  </NavLink>;

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/');
    } catch (error) {
      console.error('Error during sign out', error);
    }
  };

  return (
    <Group justify="space-between" align="center" h="100%">
      {/* <MantineLogo size={28} /> */}
      <Text
        style={{
          fontSize: 32,
          fontWeight: 700,
          color: 'var(--mantine-color-blue-5)',
        }}
      >
        <span style={{ color: 'var(--mantine-color-black)' }}>Tripp</span>py
      </Text>
      <Group visibleFrom="sm">
        <NavLink to="/" className={getClassName}>
          Home
        </NavLink>
        <AuthGuard>
          <NavLink to="/trips" className={getClassName} end={false}>
            Trips
          </NavLink>
        </AuthGuard>
        <AuthGuard>
          <NavLink to="/agent" className={getClassName}>
            Agent
          </NavLink>
        </AuthGuard>
        <NavLink to="/contact" className={getClassName}>
          Contact
        </NavLink>
      </Group>
      <Group>
        {authenticated ? (
          <>
            <Menu
              width={260}
              position="bottom-end"
              transitionProps={{ transition: 'pop-top-right' }}
              onClose={() => setUserMenuOpened(false)}
              onOpen={() => setUserMenuOpened(true)}
              withinPortal
            >
              <Menu.Target>
                <UnstyledButton className={cx(classes.user, { [classes.userActive]: userMenuOpened })}>
                  <Group gap={7}>
                    {/* <Avatar src={user.image} alt={firstName} radius="xl" size={20} /> */}
                    <IconUserCircle size={32} stroke={1} color="var(--mantine-color-blue-5)" />
                    <Text fw={500} size="md" lh={1} mr={3} c="gray.7">
                      {firstName}
                    </Text>
                    <IconChevronDown size={12} stroke={1.5} />
                  </Group>
                </UnstyledButton>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item leftSection={<IconSettings size={16} stroke={1.5} />}>Account settings</Menu.Item>
                <Menu.Divider />
                <Menu.Item onClick={handleSignOut} leftSection={<IconLogout size={16} stroke={1.5} />}>
                  Logout
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </>
        ) : (
          <>
            <Button variant="default" onClick={() => navigate('/signin')}>
              Log In
            </Button>
            <Button onClick={() => navigate('/signup')}>Sign Up</Button>
          </>
        )}
      </Group>

      <Burger opened={opened} onClick={toggle} size="sm" hiddenFrom="sm" />
    </Group>
  );
}
