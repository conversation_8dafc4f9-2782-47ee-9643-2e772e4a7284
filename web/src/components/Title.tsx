import React from 'react';
import { Title as MantineTitle, Flex } from '@mantine/core';

interface TitleProps {
  text: string;
  icon: React.ElementType;
}

const Title: React.FC<TitleProps> = ({ text, icon: IconComponent }) => {
  return (
    <MantineTitle order={2} ta="center" mb="xl">
      <Flex align="center" justify="center" gap="xs">
        <IconComponent stroke={2} size={32} color="var(--mantine-color-blue-5)" />
        {text}
      </Flex>
    </MantineTitle>
  );
};

export default Title;
