import { ReactNode, useEffect, useState } from 'react';
import { getCurrentUser } from '@aws-amplify/auth';

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode; // Optional: Content to show if not authenticated
}

const AuthGuard = ({ children, fallback = null }: AuthGuardProps) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        await getCurrentUser();
        setIsAuthenticated(true);
      } catch {
        setIsAuthenticated(false);
      }
    };

    checkAuth();
  }, []);

  if (isAuthenticated === null) return null;

  return isAuthenticated ? <>{children}</> : <>{fallback}</>;
};

export default AuthGuard;
