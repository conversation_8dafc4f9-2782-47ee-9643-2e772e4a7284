import { Navigate } from 'react-router-dom';
import { ReactNode } from 'react';
import { useAuth } from '../hooks/useAuth';

interface ProtectedRouteProps {
  children: ReactNode;
}

export const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { authenticated } = useAuth();

  if (authenticated === null) return null;

  return authenticated ? children : <Navigate to="/signin" replace />;
};
