import { Button, Image, Stack, Text } from '@mantine/core';
import noTrips from '../assets/no-trips.png';
import { useNavigate } from 'react-router-dom';
import { Trip } from '@shared/types/trip';

export const NoTrips = ({ trips }: { trips?: Trip[] }) => {
  const navigate = useNavigate();

  return (
    trips?.length === 0 && (
      <Stack align="center">
        <div>
          <Text size="xl">No trips planned just yet</Text>
        </div>
        <div>
          <Image src={noTrips} h={200} w="auto" />
        </div>

        <div>
          <Button size="xl" mt="xl" onClick={() => navigate('/trips/edit')}>
            Plan a Trip
          </Button>
        </div>
      </Stack>
    )
  );
};
