import api from './axiosInstance';
import { Trip } from '@shared/types/trip';

export const getTrips = async (): Promise<Trip[]> => {
  const response = await api.get<Trip[]>('/trips');
  return response.data;
};

export const getTripById = async (id: string): Promise<Trip> => {
  const response = await api.get<Trip>(`/trips/${id}`);
  return response.data;
};

export const saveTrip = async (trip: Trip, isEdit: boolean): Promise<Trip> => {
  const response = isEdit ? await api.put<Trip>(`/trips/${trip.id}`, trip) : await api.post<Trip>('/trips', trip);
  return response.data;
};

export const deleteTrip = async (id: string): Promise<void> => {
  return api.delete(`/trips/${id}`);
};
