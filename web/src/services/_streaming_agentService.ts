import { AxiosResponse } from 'axios';
import api from './axiosInstance';
import { AgentRequest } from '@shared/types/agent';

export const askAgent = async (
  prompt: string,
  tripId: string,
  onChunk: (content: string) => void,
  onDone: () => void,
) => {
  let previousText = '';

  await api.post<any, AxiosResponse<any>, AgentRequest>(
    '/agent',
    {
      prompt,
      tripId,
    },
    {
      responseType: 'text',
      headers: {
        Accept: 'text/event-stream',
      },
      timeout: 0,
      maxContentLength: Infinity,
      onDownloadProgress: (progressEvent) => {
        const xhr = progressEvent.event?.target as XMLHttpRequest;
        if (xhr?.responseText) {
          // Get only the new text since last update
          const newText = xhr.responseText.substring(previousText.length);
          previousText = xhr.responseText;

          // Process any complete events in the new text
          const events = newText
            .split('\n\n')
            .filter(Boolean)
            .map((event) => event.replace('data: ', ''));

          // Process each complete event
          events.forEach((event) => {
            try {
              const { text } = JSON.parse(event);
              if (text) {
                onChunk(text);
              }
            } catch (e) {
              // Ignore incomplete events
            }
          });
        }
      },
    },
  );

  onDone();
};
