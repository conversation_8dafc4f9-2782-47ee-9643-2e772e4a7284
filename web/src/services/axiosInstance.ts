import { fetchAuthSession } from '@aws-amplify/auth';
import axios from 'axios';

const getAccessToken = async () => {
  const session = await fetchAuthSession();
  return session.tokens?.accessToken;
};

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

api.interceptors.request.use(
  async (config) => {
    const accessToken = await getAccessToken(); // Get the ID token asynchronously

    if (accessToken) {
      config.headers['Authorization'] = `Bearer ${accessToken}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

export default api;
