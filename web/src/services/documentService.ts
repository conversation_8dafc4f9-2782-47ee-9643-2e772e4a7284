import api from './axiosInstance';
import axios from 'axios';
import pLimit from 'p-limit';

const directApi = axios.create();

const limit = pLimit(4);

interface UploadDocumentParams {
  tripId: string;
  file: File;
  documentId: string;
  contentType?: string;
  onProgress?: (progress: number) => void;
}

export const uploadDocument = async ({
  tripId,
  file,
  documentId,
  contentType,
  onProgress,
}: UploadDocumentParams): Promise<void> => {
  const urlResponse = await limit(() =>
    api.put<string>(`/trips/${tripId}/documents/${documentId}`, null, {
      params: {
        contentType,
      },
    }),
  );
  const uploadUrl = urlResponse.data;

  await limit(() =>
    directApi.put(uploadUrl, file, {
      headers: {
        'Content-Type': contentType,
      },
      maxBodyLength: Infinity,
      maxContentLength: Infinity,
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(percentCompleted);
        }
      },
    }),
  );
};

export const downloadDocument = async (tripId: string, documentId: string): Promise<Blob> => {
  const urlResponse = await limit(() => api.get<string>(`/trips/${tripId}/documents/${documentId}`));
  const downloadUrl = urlResponse.data;

  const response = await limit(() =>
    directApi.get(downloadUrl, {
      responseType: 'blob',
    }),
  );

  return response.data;
};
