// Base Trip interface with common properties
export interface Trip {
  id: string;
  name: string;
  dateFrom?: string;
  dateTo?: string;
  companions?: string[];
  description?: string;
  documents?: TripDocumentMetadata[];
  createdAt?: string;
  updatedAt?: string;
}

// File information returned by the server
export interface TripDocumentMetadata {
  id: string;
  parentId?: string; //for email attachments
  name?: string;
  size?: number;
  contentType?: string; //'application/pdf' | 'text/plain' | 'message/rfc822';
}
