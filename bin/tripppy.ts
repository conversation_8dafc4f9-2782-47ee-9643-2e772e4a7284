#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import * as dotenv from 'dotenv';
import { BackendStack } from '../lib/backend-stack';
import { FrontendStack } from '../lib/frontend-stack';

dotenv.config();

const app = new cdk.App();

const env = { account: '************', region: 'us-east-1' };
const domainName = 'tripppy.co';

const stack = app.node.tryGetContext('stack');

// cdk deploy -c init   If creating the stack for the first time (it will create S3 buckets and dynamodb tables)
const init: boolean = app.node.tryGetContext('init') !== undefined;

if (stack === 'frontend') {
  new FrontendStack(app, 'FrontendStack', {
    domainName,
    env,
  });
} else {
  new BackendStack(app, 'BackendStack', {
    /* If you don't specify 'env', this stack will be environment-agnostic.
     * Account/Region-dependent features and context lookups will not work,
     * but a single synthesized template can be deployed anywhere. */
    /* Uncomment the next line to specialize this stack for the AWS Account
     * and Region that are implied by the current CLI configuration. */
    // env: { account: process.env.CDK_DEFAULT_ACCOUNT, region: process.env.CDK_DEFAULT_REGION },
    /* Uncomment the next line if you know exactly what Account and Region you
     * want to deploy the stack to. */
    env,
    domainName,
    userPoolId: 'us-east-1_CJwE2wg1s',
    emailRecepient: '<EMAIL>',
    init,
    /* For more information, see https://docs.aws.amazon.com/cdk/latest/guide/environments.html */
  });
}
