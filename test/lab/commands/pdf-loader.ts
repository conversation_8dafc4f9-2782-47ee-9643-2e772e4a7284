import { WebPDFLoader } from '@langchain/community/document_loaders/web/pdf';
import { DocumentLoader } from './document-loader';

export class PdfLoader extends DocumentLoader {
  async load(content: Buffer): Promise<string> {
    const loader = new WebPDFLoader(new Blob([content]), { splitPages: false });
    const doc = await loader.load();
    const text = doc.map((page) => page.pageContent).join('\n\n');
    return text;
  }
}
