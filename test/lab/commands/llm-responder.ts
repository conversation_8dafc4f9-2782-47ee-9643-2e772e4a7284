import { Command, CommandOptions } from '../../../src/lib/pipeliner';
import { TextScore } from './pinecone-query';
import { LLMClient } from '../clients/llm-client';

export class LLMResponder implements Command<TextScore[], string, undefined, string> {
  logging: boolean;

  constructor(private readonly llm: LLMClient) {}

  async execute(input: TextScore[], { context, pipelineInput }: CommandOptions<undefined, string>): Promise<string> {
    const text = `
        You are a helpful assistant that answers questions based on the user's itinerary.
        
        This is the context:

        ${input.map((result, index) => `${index + 1}: ${result.text}`).join('\n\n')}

        Question: ${pipelineInput!}
    `;

    const response = await this.llm.ask(text);
    return response;
  }
}
