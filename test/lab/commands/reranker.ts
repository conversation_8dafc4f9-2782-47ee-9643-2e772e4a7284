import { PineconeClient } from '../clients/pinecone-client';
import { TextScore } from './pinecone-query';
import { Command, CommandOptions } from '../../../src/lib/pipeliner';
import { MyContext } from './my-context';

export class ReRanker implements Command<TextScore[], TextScore[], undefined, string> {
  logging: boolean;

  constructor(private readonly pineconeClient: PineconeClient) {}

  async execute(
    input: TextScore[],
    { context, pipelineInput }: CommandOptions<undefined, string>,
  ): Promise<TextScore[]> {
    const documents = input.map((result): string => result.text);
    const rerankResult = await this.pineconeClient.rerank(pipelineInput!, documents);

    return rerankResult.data
      .map(
        (item): TextScore => ({
          text: item.document?.text || '',
          score: item.score!,
        }),
      )
      .sort((a, b) => b.score - a.score);
  }
}
