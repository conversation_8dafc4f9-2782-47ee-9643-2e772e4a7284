import { Command } from '../../../src/lib/pipeliner';
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';

export class TextSplitter implements Command<string, string[]> {
  logging: boolean;

  constructor(
    private readonly chunkSize: number = 1000,
    private readonly chunkOverlap: number = 50,
  ) {}

  async execute(text: string): Promise<string[]> {
    const splitter = new RecursiveCharacterTextSplitter({
      chunkSize: this.chunkSize,
      chunkOverlap: this.chunkOverlap,
    });

    return splitter.splitText(text);
  }
}
