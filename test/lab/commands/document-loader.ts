import * as fs from 'fs';
import { Command } from '../../../src/lib/pipeliner';

export abstract class DocumentLoader implements Command<Buffer | string, string> {
  logging: boolean;

  async execute(input: Buffer | string): Promise<string> {
    const content = typeof input === 'string' ? fs.readFileSync(input) : input;
    return this.load(content);
  }

  abstract load(input: Buffer): Promise<string>;
}
