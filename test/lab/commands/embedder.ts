import { TextEmbedding } from '../clients/llm-client';
import { Command } from '../../../src/lib/pipeliner';

export type EmbeddingFunction = (texts: string[]) => Promise<TextEmbedding[]>;

type EmbedderOutput<T> = T extends string[] ? TextEmbedding[] : TextEmbedding;

export class Embedder<T extends string | string[]> implements Command<T, EmbedderOutput<T>> {
  logging: boolean = false;

  constructor(private readonly embeddingFunction: EmbeddingFunction) {}

  async execute(input: T): Promise<EmbedderOutput<T>> {
    if (Array.isArray(input)) {
      const embeddings = await this.embeddingFunction(input);
      return embeddings as EmbedderOutput<T>;
    } else {
      const embeddings = await this.embeddingFunction([input as string]);
      return embeddings[0] as EmbedderOutput<T>;
    }
  }
}
