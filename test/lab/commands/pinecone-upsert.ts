import { TextEmbedding } from '../clients/llm-client';
import { PineconeClient } from '../clients/pinecone-client';
import { Command } from '../../../src/lib/pipeliner';

export class PineconeUpsert implements Command<TextEmbedding[], void> {
  logging: boolean;

  constructor(private readonly pineconeClient: PineconeClient) {}

  async execute(textEmbeddings: TextEmbedding[]): Promise<void> {
    await this.pineconeClient.upsertEmbeddings(textEmbeddings);
  }
}
