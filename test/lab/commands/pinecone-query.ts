import { Command } from '../../../src/lib/pipeliner';
import { PineconeClient } from '../clients/pinecone-client';
import { TextEmbedding } from '../clients/llm-client';

export type TextScore = { text: string; score: number };

export class PineconeQuery implements Command<TextEmbedding, TextScore[]> {
  logging: boolean;

  constructor(private readonly pineconeClient: PineconeClient) {}

  async execute(embedding: TextEmbedding): Promise<TextScore[]> {
    const results = await this.pineconeClient.queryEmbedding(embedding.embedding);

    return results.map(
      (result): TextScore => ({ text: (result.metadata?.text as string) || '', score: result.score! }),
    );
  }
}
