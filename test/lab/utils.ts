export const formatMarkdown = (text: string): string => {
  return (
    text
      // Headers
      .replace(/^### (.*$)/gm, '\x1b[1m\x1b[36m### $1\x1b[0m')
      .replace(/^## (.*$)/gm, '\x1b[1m\x1b[35m## $1\x1b[0m')
      .replace(/^# (.*$)/gm, '\x1b[1m\x1b[33m# $1\x1b[0m')
      // Bold
      .replace(/\*\*(.*?)\*\*/g, '\x1b[1m$1\x1b[0m')
      // Lists
      .replace(/^- (.*$)/gm, '  \x1b[32m•\x1b[0m $1')
      .replace(/^\* (.*$)/gm, '  \x1b[32m•\x1b[0m $1')
      // Code blocks
      .replace(/`([^`]+)`/g, '\x1b[90m`$1`\x1b[0m')
      // Links (basic formatting)
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '\x1b[34m$1\x1b[0m')
  );
};
