import { <PERSON><PERSON><PERSON>oader } from './commands/email-loader';
import { Embedder } from './commands/embedder';
import { PineconeQuery } from './commands/pinecone-query';
import { PineconeUpsert } from './commands/pinecone-upsert';
import { <PERSON><PERSON><PERSON><PERSON> } from './commands/reranker';
import { LLMFactory } from './clients/llm-factory';
import { PineconeClient } from './clients/pinecone-client';
import { LLMResponder } from './commands/llm-responder';
import { PassThrough, PipelineBuilder } from '../../src/lib/pipeliner';
import 'dotenv/config';

import * as readline from 'node:readline/promises';
import { stdin as input, stdout as output } from 'node:process';
import { TextSplitter } from './commands/text-splitter';
import { formatMarkdown } from './utils';
import { MyContext } from './commands/my-context';

const rl = readline.createInterface({ input, output });

const openai = LLMFactory.createLLM('openai', process.env.OPENAI_API_KEY!);
const perplexity = LLMFactory.createLLM('perplexity', process.env.PERPLEXITY_API_KEY!);
const pineconeClient = new PineconeClient(process.env.PINECONE_API_KEY!, 'tripppy-test');

(async () => {
  try {
    const ingestData = await rl.question('\n\x1b[1m\x1b[34mIngest data?\x1b[0m (y/n) [n]: ');
    if (ingestData === 'y') {
      try {
        process.stdout.write('Deleting all embeddings... ');
        await pineconeClient.deleteAll();
        process.stdout.write('done!\n');
      } catch (error) {}

      const documents = ['test/lab/emails/merkanti.eml', 'test/lab/emails/lufthansa.eml'];

      process.stdout.write('Ingesting data... ');

      const ingestionPipeline = new PipelineBuilder<string>();

      for (const document of documents) {
        await ingestionPipeline
          .pipe(new EmailLoader())
          .pipe(new TextSplitter())
          .pipe(new Embedder(openai.embed.bind(openai)))
          .pipe(new PineconeUpsert(pineconeClient))
          .run(document);
      }

      await new Promise((resolve) => setTimeout(resolve, 15000));

      process.stdout.write('done!\n');
    }

    const queryPipeline = new PipelineBuilder<string>();

    while (true) {
      const prompt = await rl.question('\n\x1b[1m\x1b[34mPrompt: \x1b[0m');

      const context: MyContext = { prompt };

      const response = await queryPipeline
        .pipe(new Embedder(openai.embed.bind(openai)))
        .pipe(new PineconeQuery(pineconeClient))
        .pipe(new ReRanker(pineconeClient))
        .pipe(new LLMResponder(perplexity))
        // .parallel([
        //   new EmailLoader(),
        //   new TextSplitter(),
        //   new Embedder(openai.embed.bind(openai))
        // ])
        // .branch(
        //   (input, options) => input.length > 0,
        //   (thenBuilder) => thenBuilder.pipe(new EmailLoader()),
        //   (elseBuilder) => elseBuilder.pipe(new TextSplitter())
        // )
        // .catch((error, stepIndex) => {
        //   return 'Error: ' + error.message;
        // })
        .run(prompt);

      console.log('--\n' + formatMarkdown(response));

      // const pipeline = await new PipelineBuilder<string>()
      // .pipe(new EmailLoader())
      // .branch(
      //   (user, options) => user !== null,
      //   (thenBuilder) => thenBuilder.pipe(new PassThrough()),
      //   (elseBuilder) => elseBuilder.pipe(new UserCreator())
      // )
      // .switch([
      //   {
      //     when: (input) => input.type === 'email',
      //     build: (b) => b.pipe(new EmailLoader())
      //   },
      //   {
      //     when: (input) => input.type === 'pdf',
      //     build: (b) => b.pipe(new PDFLoader())
      //   },
      //   {
      //     when: () => true, // default case
      //     build: (b) => b.pipe(new FallbackLoader())
      //   }
      // ])
      // .pipe(new PassThrough())
      // .run(prompt);
    }
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
    } else {
      console.error(error);
    }
  } finally {
    rl.close();
  }
})();
