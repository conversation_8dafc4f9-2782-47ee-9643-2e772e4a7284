import { createPerplexity, PerplexityProvider } from '@ai-sdk/perplexity';
import { LLMClient, TextEmbedding } from './llm-client';
import { generateText } from 'ai';

export class PerplexityClient implements LLMClient {
  private readonly perplexity: PerplexityProvider;

  constructor(apiKey: string) {
    this.perplexity = createPerplexity({
      apiKey,
    });
  }

  async ask(prompt: string): Promise<string> {
    const { text } = await generateText({
      model: this.perplexity('sonar'),
      prompt,
    });

    return text;
  }

  async embed(texts: string[], normalize = true): Promise<TextEmbedding[]> {
    throw new Error('Not implemented.');
  }
}
