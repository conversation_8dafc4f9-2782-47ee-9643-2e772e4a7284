import { OpenAI } from 'openai';
import { LLMClient, TextEmbedding } from './llm-client';

export type Model = 'gpt-4' | 'gpt-4o';
export type EmbeddingModel = 'text-embedding-3-large' | 'text-embedding-3-small';

export class OpenAIClient implements LLMClient {
  private openai: OpenAI;

  constructor(
    private readonly apiKey: string,
    private readonly model: Model = 'gpt-4',
    private readonly embeddingModel: EmbeddingModel = 'text-embedding-3-small',
  ) {
    this.openai = new OpenAI({
      apiKey: this.apiKey,
    });
  }

  async ask(prompt: string): Promise<string> {
    try {
      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.7,
        max_tokens: 1000,
      });

      const result = response.choices[0]?.message?.content?.trim() || '';

      return result;
    } catch (error) {
      console.error('[OpenAI] Error generating response:', error);
      throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async embed(texts: string[], normalize = false): Promise<TextEmbedding[]> {
    const normalizeFunction = (embedding: number[]): number[] => {
      const norm = Math.sqrt(embedding.reduce((sum, value) => sum + value * value, 0));
      return embedding.map((value) => value / norm);
    };

    try {
      const response = await this.openai.embeddings.create({
        model: this.embeddingModel,
        input: texts,
        encoding_format: 'float',
      });

      return response.data.map((item) => ({
        text: texts[item.index],
        embedding: normalize ? normalizeFunction(item.embedding) : item.embedding,
      }));
    } catch (error) {
      console.error('[OpenAI] Error creating embedding:', error);
      throw new Error(`Failed to create embedding: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
