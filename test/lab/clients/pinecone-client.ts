import {
  Index,
  Pinecone,
  PineconeR<PERSON>ord,
  RecordMetadata,
  RerankResult,
  ScoredPineconeRecord,
} from '@pinecone-database/pinecone';
import { TextEmbedding } from './llm-client';

export class PineconeClient {
  private readonly client: Pinecone;
  private index: Index<RecordMetadata>;

  constructor(apiKey: string, indexName: string) {
    this.client = new Pinecone({
      apiKey,
    });
    this.index = this.client.Index(indexName);
  }

  async upsertEmbeddings(textEmbeddings: TextEmbedding[]) {
    const vectors: Array<PineconeRecord<RecordMetadata>> = textEmbeddings.map((textEmbedding) => ({
      id: crypto.randomUUID(),
      values: textEmbedding.embedding,
      metadata: {
        text: textEmbedding.text,
      },
    }));

    return this.index.upsert(vectors);
  }

  async queryEmbedding(embedding: number[]): Promise<ScoredPineconeRecord<RecordMetadata>[]> {
    const result = await this.index.query({
      vector: embedding,
      topK: 50,
      includeValues: false,
      includeMetadata: true,
    });

    return result.matches;
  }

  async rerank(prompt: string, documents: string[], topN = 5): Promise<RerankResult> {
    return this.client.inference.rerank('bge-reranker-v2-m3', prompt, documents, {
      topN,
      returnDocuments: true,
    });
  }

  async deleteAll(): Promise<void> {
    return this.index.deleteAll();
  }
}
