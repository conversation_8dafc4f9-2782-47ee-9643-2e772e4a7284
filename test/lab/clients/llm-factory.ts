import { LLMClient } from './llm-client';
import { OpenAIClient } from './openai-client';
import { PerplexityClient } from './perplexity-client';

type LLMProvider = 'openai' | 'perplexity';

export class LLMFactory {
  static createLLM(provider: LLMProvider, apiKey: string): LLMClient {
    switch (provider) {
      case 'openai':
        return new OpenAIClient(apiKey);
      case 'perplexity':
        return new PerplexityClient(apiKey);
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }
}
