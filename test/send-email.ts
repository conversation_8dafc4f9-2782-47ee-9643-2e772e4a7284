import Mailgun from 'mailgun.js';
import FormData from 'form-data';
import * as dotenv from 'dotenv';
import * as path from 'path';

const scriptsEnvPath = path.resolve(__dirname, '.env');
dotenv.config({ path: scriptsEnvPath });

async function sendSimpleMessage() {
  const mailgun = new Mailgun(FormData);
  const mg = mailgun.client({
    username: 'api',
    key: process.env.MAILGUN_API_KEY!,
  });
  try {
    const data = await mg.messages.create('sandbox6d80c0229bcd4ff7bc4bdd7185570062.mailgun.org', {
      from: 'Mailgun Sandbox <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: 'Hello <PERSON><PERSON><PERSON>',
      text: 'Congratulations <PERSON><PERSON><PERSON>, you just sent an email with <PERSON><PERSON>! You are truly awesome!',
    });

    console.log(data); // logs response data
  } catch (error) {
    console.log(error); //logs any error
  }
}

(async () => {
  await sendSimpleMessage();
})();
