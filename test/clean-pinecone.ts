import { Pinecone } from '@pinecone-database/pinecone';

export const cleanPinecone = async (indexName: string) => {
  try {
    // Initialize Pinecone client
    const pinecone = new Pinecone({
      apiKey: process.env.PINECONE_API_KEY!,
    });

    // Get your index name from environment variable or hardcode it
    console.log(`Connecting to Pinecone index: ${indexName}`);

    // Get the index
    const index = pinecone.index(indexName);

    // Check if we're connected to the correct index
    console.log(`Connected to index: ${indexName}`);

    // Access the default namespace
    const defaultNamespace = index.namespace('');
    console.log(`Preparing to delete all records from default namespace`);

    // Delete all vectors from the default namespace
    // This operation deletes all vectors, but keeps the namespace itself
    await defaultNamespace.deleteAll();
    console.log(`Successfully deleted all vectors from default namespace`);

    // Verify the deletion by checking stats
    console.log(`Checking namespace stats...`);
    const stats = await index.describeIndexStats();
    console.log(`Current index stats:`, JSON.stringify(stats, null, 2));

    console.log(`Cleanup operation completed successfully`);
  } catch (deleteError: any) {
    // Handle 404 error specifically (no data to delete)
    if (deleteError.name === 'PineconeNotFoundError' || (deleteError.message && deleteError.message.includes('404'))) {
      console.log(`No data found in namespace. Index is already empty.`);
    } else {
      // Re-throw any other errors
      throw deleteError;
    }
  }
};
