import { DynamoDBClient, ScanCommand, BatchWriteItemCommand } from '@aws-sdk/client-dynamodb';

export const clearDynamoDBTable = async (tableName: string) => {
  const client = new DynamoDBClient({});

  let lastEvaluatedKey: Record<string, any> | undefined;

  do {
    // Scan items in batches
    const scanParams = {
      TableName: tableName,
      Limit: 25, // Adjust based on your needs
      ...(lastEvaluatedKey && { ExclusiveStartKey: lastEvaluatedKey }),
    };

    const scanResponse = await client.send(new ScanCommand(scanParams));
    const items = scanResponse.Items;

    if (items && items.length > 0) {
      // Prepare delete requests for batch operation
      const deleteRequests = items.map((item) => {
        const key: Record<string, any> = {};
        for (const pkName in item) {
          if (pkName === 'PK' || pkName === 'SK' || pkName.endsWith('Key')) {
            key[pkName] = item[pkName];
          }
        }
        return { DeleteRequest: { Key: key } };
      });

      // Batch delete items
      const batchParams = {
        RequestItems: {
          [tableName]: deleteRequests,
        },
      };

      await client.send(new BatchWriteItemCommand(batchParams));
    }

    lastEvaluatedKey = scanResponse.LastEvaluatedKey;
  } while (lastEvaluatedKey);

  console.log(`All items removed from ${tableName}`);
};
