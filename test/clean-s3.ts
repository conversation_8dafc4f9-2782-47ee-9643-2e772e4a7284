import { S3Client, ListObjectsV2Command, DeleteObjectsCommand } from '@aws-sdk/client-s3';

export const emptyS3Bucket = async (bucketName: string) => {
  const client = new S3Client({});

  console.log(`Emptying S3 bucket: ${bucketName} (including all folders)`);
  let isTruncated = true;
  let continuationToken: string | undefined;
  let deletedCount = 0;

  while (isTruncated) {
    // List objects in the bucket (up to 1000 at a time)
    // This will include objects in all "folders" (prefixes)
    const listParams = {
      Bucket: bucketName,
      ContinuationToken: continuationToken,
      // Not setting a Prefix means we get everything in all folders
    };

    const listResponse = await client.send(new ListObjectsV2Command(listParams));

    // Check if there are any objects to delete
    if (listResponse.Contents && listResponse.Contents.length > 0) {
      // Create the array of objects to delete
      const objectsToDelete = listResponse.Contents.map((obj) => ({ Key: obj.Key }));

      // Log what's being deleted
      console.log(`Preparing to delete ${objectsToDelete.length} objects:`);
      objectsToDelete.forEach((obj, i) => {
        if (i < 5 || i >= objectsToDelete.length - 5) {
          console.log(`  - ${obj.Key}`);
        } else if (i === 5) {
          console.log(`  ... and ${objectsToDelete.length - 10} more ...`);
        }
      });

      // Delete the batch of objects
      const deleteParams = {
        Bucket: bucketName,
        Delete: {
          Objects: objectsToDelete,
          Quiet: false,
        },
      };

      const deleteResponse = await client.send(new DeleteObjectsCommand(deleteParams));

      // Log results
      deletedCount += objectsToDelete.length;
      console.log(`Deleted ${objectsToDelete.length} objects (total: ${deletedCount})`);

      // Handle errors if any
      if (deleteResponse.Errors && deleteResponse.Errors.length > 0) {
        console.error(
          'Some objects could not be deleted:',
          deleteResponse.Errors.map((err) => `${err.Key}: ${err.Code} - ${err.Message}`).join('\n')
        );
      }
    } else {
      console.log('No objects found to delete.');
    }

    // Check if there are more objects to fetch
    isTruncated = listResponse.IsTruncated || false;
    continuationToken = listResponse.NextContinuationToken;
  }

  console.log(`Successfully emptied bucket: ${bucketName}`);
};
