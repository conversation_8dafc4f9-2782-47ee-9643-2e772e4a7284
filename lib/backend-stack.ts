import { Stack, StackProps } from 'aws-cdk-lib';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import { Construct } from 'constructs';
import { Database } from './constructs/database';
import { TripApi } from './constructs/trip-api';
import { Parameters } from './constructs/parameters';
import { AgentApi } from './constructs/agent-api';
import { DocumentApi } from './constructs/document-api';
import { DocumentStore } from './constructs/document-store';
import { EmailCollector } from './constructs/email-collector';
import { EmailTemplates } from './constructs/email-templates';
import { RestApi } from './constructs/rest-api';
import { init } from 'openai/_shims';

interface BackendStackProps extends StackProps {
  init: boolean;
  domainName: string;
  userPoolId: string;
  emailRecepient: string;
}

export class BackendStack extends Stack {
  constructor(scope: Construct, id: string, props: BackendStackProps) {
    super(scope, id, props);

    const userPool = cognito.UserPool.fromUserPoolId(this, 'TripppyUserPool', props.userPoolId);

    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(this, 'ApiAuthorizer', {
      cognitoUserPools: [userPool],
    });

    const options = {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      authorizationScopes: ['email', 'openid', 'profile'],
    };

    const parameters = new Parameters(this, 'TripppySecrets');

    const documentStore = new DocumentStore(this, 'DocumentsBucket', { init: props.init });

    const database = new Database(this, 'TripppyTable', { init: props.init });

    const resource = new RestApi(this, 'RestApi', {
      domainName: props.domainName,
    }).root;

    const tripApi = new TripApi(this, 'TripApi', {
      resource,
      table: database.table,
      documentsBucket: documentStore.bucket,
      options,
      apiKeys: {
        openaiApiKey: parameters.openaiApiKey,
        pineconeApiKey: parameters.pineconeApiKey,
      },
    });

    new AgentApi(this, 'AgentApi', {
      resource,
      options,
      table: database.table,
      apiKeys: {
        openaiApiKey: parameters.openaiApiKey,
        perplexityApiKey: parameters.perplexityApiKey,
        pineconeApiKey: parameters.pineconeApiKey,
        telegramBotToken: parameters.telegramBotToken,
      },
    });

    new DocumentApi(this, 'DocumentApi', {
      resource: tripApi.tripIdResource,
      documentsBucket: documentStore.bucket,
      options,
    });

    new EmailCollector(this, {
      bucket: documentStore.bucket,
      userPool,
      emailRecepient: props.emailRecepient,
      table: database.table,
    });

    const emailTemplates = new EmailTemplates(this, 'EmailTemplates');
    const emailSenderUrl = emailTemplates.emailSenderUrl;

    // Needed for streaming api only
    // const httpApi = new apigatewayv2.HttpApi(this, 'StreamingTripppyApi', {
    //   apiName: 'Streaming Tripppy API',
    //   corsPreflight: {
    //     allowOrigins: ['*'],
    //     allowMethods: [apigatewayv2.CorsHttpMethod.POST, apigatewayv2.CorsHttpMethod.OPTIONS],
    //     allowHeaders: ['Content-Type'],
    //   },
    // });
    // new AgentApi(this, 'AgentApi', {
    //   httpApi,
    //   options,
    //   apiKeys: {
    //     openaiApiKey: parameters.openaiApiKey,
    //     perplexityApiKey: parameters.perplexityApiKey,
    //     pineconeApiKey: parameters.pineconeApiKey,
    //   },
    // });
  }
}
