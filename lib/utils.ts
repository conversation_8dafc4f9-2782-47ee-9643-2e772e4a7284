import { NodejsFunction, NodejsFunctionProps } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Construct } from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as path from 'path';
import * as changeCase from 'change-case';
import { Duration } from 'aws-cdk-lib/core';

export const createLambda = (
  scope: Construct,
  name: string,
  { entry, ...props }: NodejsFunctionProps & { entry: string }
) => {
  return new NodejsFunction(scope, changeCase.pascalCase(name), {
    functionName: name,
    runtime: lambda.Runtime.NODEJS_22_X,
    handler: 'handler',
    bundling: {
      minify: true,
      sourceMap: true,
      externalModules: ['aws-sdk'],
    },
    timeout: Duration.seconds(30),
    memorySize: 256,
    entry: path.join(__dirname, `../src/handlers/${entry}`),
    ...props,
  });
};
