import { Construct } from 'constructs';
import * as cr from 'aws-cdk-lib/custom-resources';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import type { CreateTemplateRequest } from '@aws-sdk/client-ses';
import { createLambda } from '../utils';

export class EmailTemplates extends Construct {
  public readonly emailSenderUrl: lambda.FunctionUrl;

  constructor(scope: Construct, id: string) {
    super(scope, id);

    this.emailSenderUrl = this.createEmailSender();

    this.createSesTemplateResource({
      Template: {
        TemplateName: 'WelcomeTemplate',
        SubjectPart: 'Hello!',
        TextPart: 'Welcome!',
        HtmlPart: '<p>Welcome!</p>',
      },
    });
  }

  private createEmailSender(): lambda.FunctionUrl {
    const handler = createLambda(this, 'sendTemplatedEmail', {
      entry: 'email/send-email.ts',
    });

    // Allow Lambda to send templated emails via SES
    handler.addToRolePolicy(
      new iam.PolicyStatement({
        actions: ['ses:SendTemplatedEmail'],
        resources: ['*'], // Or scope to verified identities if preferred
      })
    );

    // Optional: expose Lambda Function URL
    return handler.addFunctionUrl({
      authType: lambda.FunctionUrlAuthType.NONE, // Use IAM or NONE
    });
  }

  private createSesTemplateResource(templateProps: CreateTemplateRequest): cr.AwsCustomResource {
    return new cr.AwsCustomResource(this, `SesTemplate-${templateProps.Template!.TemplateName}`, {
      onCreate: {
        service: 'SES',
        action: 'createTemplate',
        parameters: templateProps,
        physicalResourceId: cr.PhysicalResourceId.of(templateProps.Template!.TemplateName!),
      },
      onUpdate: {
        service: 'SES',
        action: 'updateTemplate',
        parameters: templateProps,
      },
      onDelete: {
        service: 'SES',
        action: 'deleteTemplate',
        parameters: {
          TemplateName: templateProps.Template!.TemplateName,
        },
      },
      policy: cr.AwsCustomResourcePolicy.fromStatements([
        new iam.PolicyStatement({
          actions: ['ses:CreateTemplate', 'ses:UpdateTemplate', 'ses:DeleteTemplate'],
          resources: ['*'],
        }),
      ]),
    });
  }
}
