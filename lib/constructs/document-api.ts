import { Construct } from 'constructs';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { createLambda } from '../utils';
import { MethodOptions } from 'aws-cdk-lib/aws-apigateway/lib/method';

interface DocumentApiProps {
  resource: apigateway.Resource;
  documentsBucket: s3.IBucket;
  options: MethodOptions;
}

export class DocumentApi extends Construct {
  constructor(scope: Construct, id: string, private props: DocumentApiProps) {
    super(scope, id);

    const tripIdResource = props.resource;

    const documentsResource = tripIdResource.addResource('documents');
    const documentsIdResource = documentsResource.addResource('{documentId}');

    this.getDownloadUrl(documentsIdResource);
    this.getUploadUrl(documentsIdResource);
    this.deleteDocuments(documentsResource);
  }

  private getDownloadUrl(resource: apigateway.Resource) {
    const handler = createLambda(this, 'downloadUrl', {
      entry: '/documents/download-url.ts',
    });

    this.props.documentsBucket.grantRead(handler);

    resource.addMethod('GET', new apigateway.LambdaIntegration(handler), this.props.options);
  }

  private getUploadUrl(resource: apigateway.Resource) {
    const handler = createLambda(this, 'uploadUrl', {
      entry: '/documents/upload-url.ts',
    });

    this.props.documentsBucket.grantWrite(handler);

    resource.addMethod('PUT', new apigateway.LambdaIntegration(handler), {
      ...this.props.options,
      requestParameters: {
        'method.request.querystring.contentType': true,
      },
    });
  }

  private deleteDocuments(resource: apigateway.Resource) {
    const handler = createLambda(this, 'deleteDocuments', {
      entry: '/documents/delete-documents.ts',
    });

    this.props.documentsBucket.grantDelete(handler);

    resource.addMethod('DELETE', new apigateway.LambdaIntegration(handler), this.props.options);
  }
}
