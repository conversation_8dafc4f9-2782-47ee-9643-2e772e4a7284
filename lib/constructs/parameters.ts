import { Construct } from 'constructs';
import * as ssm from 'aws-cdk-lib/aws-ssm';

export class Parameters extends Construct {
  public readonly openaiApiKey: ssm.StringParameter;
  public readonly perplexityApiKey: ssm.StringParameter;
  public readonly pineconeApiKey: ssm.StringParameter;
  public readonly telegramBotToken: ssm.StringParameter;

  constructor(scope: Construct, id: string) {
    super(scope, id);

    this.openaiApiKey = new ssm.StringParameter(this, 'OpenaiApiKey', {
      parameterName: 'openaiApiKey',
      stringValue: process.env.OPENAI_API_KEY!,
      description: 'API Key for external service',
      tier: ssm.ParameterTier.STANDARD,
    });

    this.perplexityApiKey = new ssm.StringParameter(this, 'PerplexityApiKey', {
      parameterName: 'perplexityApiKey',
      stringValue: process.env.PERPLEXITY_API_KEY!,
      description: 'API Key for external service',
      tier: ssm.ParameterTier.STANDARD,
    });

    this.pineconeApiKey = new ssm.StringParameter(this, 'PineconeApiKey', {
      parameterName: 'pineconeApiKey',
      stringValue: process.env.PINECONE_API_KEY!,
      description: 'API Key for external service',
      tier: ssm.ParameterTier.STANDARD,
    });

    this.telegramBotToken = new ssm.StringParameter(this, 'TelegramBotToken', {
      parameterName: 'telegramBotToken',
      stringValue: process.env.TELEGRAM_BOT_TOKEN!,
      description: 'API Key for external service',
      tier: ssm.ParameterTier.STANDARD,
    });
  }
}
