import { Construct } from 'constructs';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import { RemovalPolicy } from 'aws-cdk-lib';

export interface DatabaseProps {
  init: boolean;
}

export class Database extends Construct {
  public readonly table: dynamodb.Table;

  constructor(scope: Construct, id: string, { init }: DatabaseProps) {
    super(scope, id);

    const tableName = 'Tripppy';

    if (init) {
      // Create the main DynamoDB table
      this.table = new dynamodb.Table(this, id, {
        tableName,
        partitionKey: { name: 'PK', type: dynamodb.AttributeType.STRING },
        sortKey: { name: 'SK', type: dynamodb.AttributeType.STRING },
        billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
        removalPolicy: RemovalPolicy.RETAIN,
        pointInTimeRecovery: false,
      });
    } else {
      this.table = dynamodb.Table.fromTableName(this, id, tableName) as dynamodb.Table;
    }
  }
}
