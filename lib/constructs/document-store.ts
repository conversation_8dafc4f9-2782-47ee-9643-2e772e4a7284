import { Construct } from 'constructs';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Duration, RemovalPolicy } from 'aws-cdk-lib';

export interface DocumentStoreProps {
  init: boolean;
}

export class DocumentStore extends Construct {
  public readonly bucket: s3.Bucket;
  public readonly uploadPolicy: iam.Policy;

  constructor(scope: Construct, id: string, { init }: DocumentStoreProps) {
    super(scope, id);

    const bucketName = 'tripppy-documents';

    if (init) {
      this.bucket = new s3.Bucket(this, id, {
        bucketName,
        cors: [
          {
            allowedHeaders: ['*'],
            allowedMethods: [
              s3.HttpMethods.GET,
              s3.HttpMethods.PUT,
              s3.HttpMethods.POST,
              s3.HttpMethods.DELETE,
              s3.HttpMethods.HEAD,
            ],
            allowedOrigins: ['*'],
            exposedHeaders: [
              'ETag',
              'x-amz-server-side-encryption',
              'x-amz-request-id',
              'x-amz-id-2',
              'Content-Length',
              'Content-Type',
            ],
          },
        ],
        removalPolicy: RemovalPolicy.RETAIN,
        lifecycleRules: [
          {
            tagFilters: { temporary: 'true' },
            expiration: Duration.days(1),
          },
        ],
      });
    } else {
      this.bucket = s3.Bucket.fromBucketName(this, id, bucketName) as s3.Bucket;
    }
  }
}
