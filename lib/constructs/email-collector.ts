import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as s3n from 'aws-cdk-lib/aws-s3-notifications';
import { Construct } from 'constructs';
import { createLambda } from '../utils';
import * as ses from 'aws-cdk-lib/aws-ses';
import * as actions from 'aws-cdk-lib/aws-ses-actions';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as iam from 'aws-cdk-lib/aws-iam';

const INCOMING_EMAIL_FOLDER = 'emails/';

interface EmailCollectorProps {
  bucket: s3.Bucket;
  userPool: cognito.IUserPool;
  emailRecepient: string;
  table: dynamodb.Table;
}

export class EmailCollector extends Construct {
  constructor(scope: Construct, { bucket, userPool, emailRecepient, table }: EmailCollectorProps) {
    super(scope, 'EmailCollector');

    const currentAccount = cdk.Stack.of(this).account;

    // bucket.addToResourcePolicy(
    //   new iam.PolicyStatement({
    //     actions: ['s3:PutObject'],
    //     principals: [new iam.ServicePrincipal('ses.amazonaws.com')],
    //     resources: [bucket.arnForObjects('*')],
    //     conditions: {
    //       StringEquals: {
    //         'aws:SourceAccount': currentAccount,
    //       },
    //     },
    //   })
    // );

    new s3.BucketPolicy(this, 'BucketPolicy', {
      bucket: bucket,
    }).document.addStatements(
      new iam.PolicyStatement({
        actions: ['s3:PutObject'],
        principals: [new iam.ServicePrincipal('ses.amazonaws.com')],
        resources: [bucket.arnForObjects('*')],
        conditions: {
          StringEquals: {
            'aws:SourceAccount': currentAccount,
          },
        },
      })
    );

    // Create SES receipt rule set (needs to be manually activated)
    const ruleSet = new ses.ReceiptRuleSet(this, 'IncomingEmailRuleSet', {
      receiptRuleSetName: 'incoming-email-rule-set',
    });

    // Create SES receipt rule
    new ses.ReceiptRule(this, 'IncomingEmailRule', {
      ruleSet,
      receiptRuleName: 'incoming-email-rule',
      enabled: true,
      scanEnabled: true,
      recipients: [emailRecepient],
      actions: [
        new actions.S3({
          bucket,
          objectKeyPrefix: INCOMING_EMAIL_FOLDER,
        }),
      ],
    });

    const handler = createLambda(this, 'processEmail', {
      entry: '/email/process-email.ts',
      environment: {
        USER_POOL_ID: userPool.userPoolId,
        PINECONE_API_KEY: process.env.PINECONE_API_KEY!,
        OPENAI_API_KEY: process.env.OPENAI_API_KEY!,
        PERPLEXITY_API_KEY: process.env.PERPLEXITY_API_KEY!,
        TOP_K: process.env.TOP_K!,
        SCORE_THRESHOLD: process.env.SCORE_THRESHOLD!,
      },
    });

    // Grant DynamoDB permissions
    table.grantReadWriteData(handler);

    // Grant Cognito permissions
    userPool.grant(handler, 'cognito-idp:AdminGetUser', 'cognito-idp:AdminCreateUser', 'cognito-idp:ListUsers');

    // Grant S3 permissions
    bucket.grantReadWrite(handler);

    // Add CopyObject permission using the correct action format
    //FIXME might be not needed since I am not copying/deleting emails anymore
    handler.addToRolePolicy(
      new iam.PolicyStatement({
        actions: ['s3:CopyObject'],
        resources: [bucket.arnForObjects('*')],
      })
    );

    // Add S3 trigger
    bucket.addEventNotification(s3.EventType.OBJECT_CREATED, new s3n.LambdaDestination(handler), {
      prefix: INCOMING_EMAIL_FOLDER,
    });
  }
}
