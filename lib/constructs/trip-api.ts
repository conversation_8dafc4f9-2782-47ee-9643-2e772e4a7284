import { Construct } from 'constructs';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { createLambda } from '../utils';
import { MethodOptions } from 'aws-cdk-lib/aws-apigateway/lib/method';
import * as ssm from 'aws-cdk-lib/aws-ssm';

interface TripApiProps {
  resource: apigateway.IResource;
  table: dynamodb.Table;
  documentsBucket: s3.IBucket;
  options: MethodOptions;
  apiKeys: {
    openaiApiKey: ssm.StringParameter;
    pineconeApiKey: ssm.StringParameter;
  };
}

export class TripApi extends Construct {
  public readonly tripIdResource: apigateway.Resource;

  constructor(scope: Construct, id: string, private props: TripApiProps) {
    super(scope, id);

    const resource = this.props.resource.addResource('trips');
    this.tripIdResource = resource.addResource('{tripId}');

    this.getTrips(resource, this.tripIdResource);
    this.saveTrip(resource, this.tripIdResource);
    this.deleteTrip(this.tripIdResource);
  }

  private getTrips(resource: apigateway.Resource, idResource: apigateway.Resource) {
    const handler = createLambda(this, 'getTrips', {
      entry: '/trips/get-trips.ts',
    });

    this.props.table.grantReadData(handler);

    resource.addMethod('GET', new apigateway.LambdaIntegration(handler), this.props.options);
    idResource.addMethod('GET', new apigateway.LambdaIntegration(handler), this.props.options);
  }

  private saveTrip(resource: apigateway.Resource, idResource: apigateway.Resource) {
    const handler = createLambda(this, 'saveTrip', {
      entry: '/trips/save-trip.ts',
      environment: {
        PINECONE_API_KEY: this.props.apiKeys.pineconeApiKey.stringValue,
        OPENAI_API_KEY: this.props.apiKeys.openaiApiKey.stringValue,
        TOP_K: process.env.TOP_K!,
      },
    });

    this.props.documentsBucket.grantReadWrite(handler);
    this.props.table.grantWriteData(handler);

    resource.addMethod('POST', new apigateway.LambdaIntegration(handler), this.props.options);
    idResource.addMethod('PUT', new apigateway.LambdaIntegration(handler), this.props.options);
  }

  private deleteTrip(idResource: apigateway.Resource) {
    const handler = createLambda(this, 'deleteTrip', {
      entry: '/trips/delete-trip.ts',
      environment: {
        PINECONE_API_KEY: this.props.apiKeys.pineconeApiKey.stringValue,
        OPENAI_API_KEY: this.props.apiKeys.openaiApiKey.stringValue,
      },
    });

    this.props.table.grantWriteData(handler);

    idResource.addMethod('DELETE', new apigateway.LambdaIntegration(handler), this.props.options);
  }
}
