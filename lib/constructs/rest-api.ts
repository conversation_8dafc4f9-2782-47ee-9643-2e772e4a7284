import { Construct } from 'constructs';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import { Cors } from 'aws-cdk-lib/aws-apigateway';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import * as targets from 'aws-cdk-lib/aws-route53-targets';

export interface RestApiProps {
  domainName: string;
}

export class RestApi extends Construct {
  public readonly root: apigateway.IResource;

  constructor(scope: Construct, id: string, { domainName }: RestApiProps) {
    super(scope, id);

    const apiDomainName = `api.${domainName}`;

    const zone = route53.HostedZone.fromLookup(this, 'Zone', {
      domainName,
    });

    // Create or import certificate (must be in us-east-1 for edge-optimized APIs)
    const apiCertificate = new acm.Certificate(this, 'ApiCertificate', {
      domainName: apiDomainName,
      validation: acm.CertificateValidation.fromDns(zone),
    });

    const restApi = new apigateway.RestApi(this, 'TripppyApi', {
      restApiName: 'Tripppy API',
      defaultCorsPreflightOptions: {
        allowOrigins: Cors.ALL_ORIGINS,
        allowMethods: Cors.ALL_METHODS,
        allowHeaders: Cors.DEFAULT_HEADERS,
      },
      domainName: {
        domainName: apiDomainName,
        certificate: apiCertificate,
      },
      binaryMediaTypes: ['application/pdf'],
      deployOptions: {
        stageName: 'prod',
        // Optional: Add other stage configuration
        throttlingRateLimit: 1000,
        throttlingBurstLimit: 500,
        // Optional: Enable logging
        // loggingLevel: apigateway.MethodLoggingLevel.INFO,
        // dataTraceEnabled: true,
        // metricsEnabled: true,
      },
    });

    // Create DNS record
    new route53.ARecord(this, 'ApiDnsRecord', {
      zone,
      recordName: 'api',
      target: route53.RecordTarget.fromAlias(new targets.ApiGateway(restApi)),
    });

    // Add base path mapping to connect domain to API stage
    new apigateway.BasePathMapping(this, 'ApiBasePathMapping', {
      domainName: restApi.domainName!,
      restApi,
      stage: restApi.deploymentStage,
      basePath: 'v1', // This maps api.tripppy.co/v1 to your API
    });

    this.root = restApi.root;
  }
}
