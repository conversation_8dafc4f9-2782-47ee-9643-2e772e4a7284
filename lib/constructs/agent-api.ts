import { Construct } from 'constructs';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import { MethodOptions } from 'aws-cdk-lib/aws-apigateway/lib/method';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { createLambda } from '../utils';
import { Duration } from 'aws-cdk-lib/core';

interface AgentApiProps {
  resource: apigateway.IResource;
  options: MethodOptions;
  table: dynamodb.Table;
  apiKeys: {
    openaiApiKey: ssm.StringParameter;
    perplexityApiKey: ssm.StringParameter;
    pineconeApiKey: ssm.StringParameter;
    telegramBotToken: ssm.StringParameter;
  };
}

export class AgentApi extends Construct {
  private readonly environment = {
    PINECONE_API_KEY: this.props.apiKeys.pineconeApiKey.stringValue,
    OPENAI_API_KEY: this.props.apiKeys.openaiApiKey.stringValue,
    PERPLEXITY_API_KEY: this.props.apiKeys.perplexityApiKey.stringValue,
    TOP_K: process.env.TOP_K!,
    SCORE_THRESHOLD: process.env.SCORE_THRESHOLD!,
  };

  constructor(scope: Construct, id: string, private props: AgentApiProps) {
    super(scope, id);

    const agentResource = this.props.resource.addResource('agent');
    const telegramResource = this.props.resource.addResource('telegram');

    this.ask(agentResource);
    this.telegram(telegramResource);
  }

  private ask(resource: apigateway.Resource) {
    const handler = createLambda(this, 'ask', {
      entry: '/agent/ask-agent.ts',
      environment: this.environment,
      timeout: Duration.seconds(30),
      memorySize: 256,
    });

    resource.addMethod('POST', new apigateway.LambdaIntegration(handler), this.props.options);
  }

  private telegram(resource: apigateway.Resource) {
    const handler = createLambda(this, 'telegram', {
      entry: '/agent/ask-telegram.ts',
      environment: {
        ...this.environment,
        TELEGRAM_BOT_TOKEN: this.props.apiKeys.telegramBotToken.stringValue,
      },
      timeout: Duration.seconds(30),
      memorySize: 256,
    });

    this.props.table.grantReadData(handler);

    resource.addMethod('POST', new apigateway.LambdaIntegration(handler));
  }
}
