import { Construct } from 'constructs';
import * as apigatewayv2 from 'aws-cdk-lib/aws-apigatewayv2';
import * as integrations from 'aws-cdk-lib/aws-apigatewayv2-integrations';
import { MethodOptions } from 'aws-cdk-lib/aws-apigateway/lib/method';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { createLambda } from '../utils';
import { Duration } from 'aws-cdk-lib/core';
import * as iam from 'aws-cdk-lib/aws-iam';

interface AgentApiProps {
  httpApi: apigatewayv2.HttpApi;
  options: MethodOptions;
  apiKeys: {
    openaiApiKey: ssm.StringParameter;
    perplexityApiKey: ssm.StringParameter;
    pineconeApiKey: ssm.StringParameter;
  };
}

export class AgentApi extends Construct {
  constructor(scope: Construct, id: string, private props: AgentApiProps) {
    super(scope, id);

    this.ask();
  }

  private ask() {
    const handler = createLambda(this, 'ask', {
      entry: '/agent/ask-agent.ts',
      environment: {
        PINECONE_API_KEY: this.props.apiKeys.pineconeApiKey.stringValue,
        OPENAI_API_KEY: this.props.apiKeys.openaiApiKey.stringValue,
        PERPLEXITY_API_KEY: this.props.apiKeys.perplexityApiKey.stringValue,
      },
      timeout: Duration.seconds(30),
      memorySize: 256,
    });

    handler.addPermission('InvokeFromApiGateway', {
      principal: new iam.ServicePrincipal('apigateway.amazonaws.com'),
      action: 'lambda:InvokeFunction',
    });

    this.props.httpApi.addRoutes({
      path: '/api/v1/agent',
      methods: [apigatewayv2.HttpMethod.POST],
      integration: new integrations.HttpLambdaIntegration('LambdaIntegration', handler, {
        payloadFormatVersion: apigatewayv2.PayloadFormatVersion.VERSION_2_0,
      }),
      // authorizer: this.props.options.authorizer,
      // authorizationScopes: this.props.options.authorizationScopes,
    });
  }
}
